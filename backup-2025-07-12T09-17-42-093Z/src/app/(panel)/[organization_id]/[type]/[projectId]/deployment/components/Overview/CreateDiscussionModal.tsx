// deployment/components/Overview/CreateDiscussionModal.tsx
//@ts-nocheck
import React from 'react';

interface CreateDiscussionModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const CreateDiscussionModal: React.FC<CreateDiscussionModalProps> = ({ 
  isOpen, 
  onClose 
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-[800px]">
        <h2 className="typography-heading-4 font-weight-semibold mb-4">Create Discussion</h2>
        {/* Placeholder for your discussion modal content */}
        <div className="h-96 flex items-center justify-center text-gray-500">
          Discussion Modal Content will go here
        </div>
        <div className="flex justify-end mt-4">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default CreateDiscussionModal;