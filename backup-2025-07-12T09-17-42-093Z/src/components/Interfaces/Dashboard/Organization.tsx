export interface OrganizationState {
  name: string;
  business_email: string;
  industrial_type: string;
  company_size: string;
  domain: string;
  image: string;
  plan_id: string;
  admin_name: string;
  admin_email: string;
  admin_contact_number: string;
  admin_department: string;
  credits: number;
  configurations: {
    max_users: number;
    role_customization: boolean;
    api_access: boolean;
    github_integration: boolean;
    jira_integration: boolean;
    custom_reports: boolean;
    export_capabilities: boolean;
  };
  settings: {
    showfunctioncalling: boolean;
  };
}
