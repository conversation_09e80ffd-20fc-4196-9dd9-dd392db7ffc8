"use client";
import React, { useState, useEffect, useContext } from "react";
import { useRouter, usePara<PERSON>, useSearchParams, usePathname } from "next/navigation";
import { Globe } from 'lucide-react';
import axios from "axios";
import dynamic from "next/dynamic";
import ConfigureButtons from "@/components/ConfigureButtons/ConfigureButtons";
import CodeGenerationModal from "@/app/modal/CodeGenerationModal";
import { ArchitectureContext } from "@/components/Context/ArchitectureContext";
import { useCodeGeneration } from "@/components/Context/CodeGenerationContext";
import FigmaSection from "./FigmaSection";
import { getPastCodeGenerationTasks } from "@/utils/batchAPI";
import { getRepository } from "@/utils/repositoryAPI";
import en from "../../../en.json";
import { getHeadersRaw, updateNodeByPriority } from "@/utils/api";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import PropertiesRenderer from "@/components/UiMetadata/PropertiesRenderer";
import PastTasksModal from "@/components/Modal/PastTasksModal";
import Badge from "@/components/UIComponents/Badge/Badge";
import ErrorView from "@/components/Modal/ErrorViewModal";
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import { Code, Braces, Layers, BookOpen } from 'lucide-react';
import { Workflow, Share2, GitBranch } from 'lucide-react';
import { Settings, Eye, ArrowLeft, Database } from "lucide-react";
import { CheckCircle2, AlertTriangle, Info } from 'lucide-react';
import CodeGenerationHandler from "@/app/modal/CodeGenerationHandler";
import { IconButton } from '@/components/UIComponents/Buttons/IconButton';
import { updateSessionStorageBackHistory } from "@/utils/helpers";
import NavigationTree from "@/components/Modal/NavigationTreeComponent"
import { TwoColumnSkeletonLoader } from "@/components/UIComponents/Loaders/LoaderGroup"
import { BranchSelector } from "@/components/Git/BranchSelector";
import { useResponsiveDimensions } from "@/utils/responsiveness";
import RepositoryDetailsModal from "@/components/Modal/RepositoryModal";
import CodeGenerationSetupModal from "@/app/modal/CodeGenerationSetupModal";
import { PLATFORMS, frameworks, Generic, backendFrameworks, mobileFrameworks } from "@/constants/code_gen/platforms";
import { TOOLTIP_CONTENT } from "@/utils/constant/tooltip";
import { ChevronDown, ChevronUp } from 'lucide-react';
import { createDesignNode } from "@/utils/architectureAPI";
import { buildProjectUrl } from '@/utils/navigationHelpers';
const NoSSR = dynamic(() => import("../../Chart/MermaidChart"), { ssr: false });


export const ItemDetails = () => {
  const { showAlert } = useContext(AlertContext);
  const { lowLevelDesignVal } = useContext(ArchitectureContext);
  const router = useRouter();
  const params = useParams();
  const searchParams = useSearchParams();
  const [isGeneratingCode, setIsGeneratingCode] = useState(false);
  const { isVisible } = useCodeGeneration();
  const pathname = usePathname();
  const projectId = params.projectId;
  const architectureLeafId = params.architectureLeafId;
  const [details, setDetails] = useState({});
  const [designCreating, setDesignCreating] = useState(false);
  const [loading, setLoading] = useState(true);
  const [isPastTasksCodeModalOpen, setIsPastTasksCodeModalOpen] = useState(false);
  const [pastTasks, setPastTasks] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [limit, setLimit] = useState(10);
  const [skip, setSkip] = useState(0);
  const [error, setError] = useState(null);
  const [currentBranch, setCurrentBranch] = useState(null)
  const { mainContentWidth, contentHeight, calculateDimensions } = useResponsiveDimensions();
  const [showRepoDetails, setShowRepoDetails] = useState(false);
  const [currentPlatform, setCurrentPlatform] = useState({ key: "generic", label: "Generic", icon: <Generic /> });
  const [codeGenSetupModal, setCodeGenSetupModal] = useState(false);
  const [repository, setRepository] = useState(null);
  const [isPastTaskLoading, setIsPastTaskLoading] = useState(false);
  const [currentFramework, setCurrentFramework] = useState(frameworks[0]);

  const handleFigmaUpdate = () => {
    fetchLowLevelDesignDetails();
  };

  const handlePlatformChange = (platformData) => {
    setCurrentPlatform(platformData);
    handlePropertyUpdate("platform", platformData.key);
    if(platformData?.key=="mobile")
      setCurrentFramework(mobileFrameworks[0]);

    if(platformData?.key=="web")
      setCurrentFramework(frameworks[0]);

    if(platformData?.key=="backend")
      setCurrentFramework(backendFrameworks[0]);
    }

  useEffect(() => {
    if (currentFramework){
      handlePropertyUpdate("framework", currentFramework.key);
    }
  }, [currentFramework])

  const handleSetPastTasks = (tasks, skip) => {
    let newTasks = []
    newTasks.push(...tasks);


    setPastTasks(newTasks);
  }

  const fetchPastTasks = async (currentSkip = 0, currentLimit = limit) => {
    setIsPastTaskLoading(true);
    try {
      const result = await getPastCodeGenerationTasks(
        projectId,
        architectureLeafId,
        currentLimit,
        currentSkip
      );
      handleSetPastTasks(result.tasks, currentSkip);
      setTotalCount(result.total_count);
      setSkip(currentSkip);
      setLimit(currentLimit);
    } catch (error) {

      showAlert("Failed to fetch past code generation tasks", "error");
    } finally {
      setIsPastTaskLoading(false);
    }
  };

  const handlePropertyUpdate = async (key, value) => {
    try {
      if (key === 'platform' || key === 'framework') {
        // showAlert("Please wait...", "info");
      }
      if (!details?.id) {
        throw new Error('Design node ID not found');
      }

      const response = await updateNodeByPriority(details.id, key, value);

      if (response === "success") {
        setDetails(prev => ({
          ...prev,
          properties: {
            ...prev.properties,
            [key]: value
          }
        }));
        if (key == "platform") {
          showAlert("Platform updated successfully", "success");
        }
        else {
          showAlert("Content updated successfully", "success");
        }
      } else {
        throw new Error('Update failed');
      }
    } catch (error) {

    }
  };

   const cleanDescription = (description) => {
        if (!description) return '';

        return description
            .replace(/#+\s/g, '')        // Remove markdown headers (# Header)
            .replace(/\*\*/g, '')        // Remove bold (**text**)
            .replace(/\*/g, '')          // Remove italics (*text*)
            .replace(/`/g, '')           // Remove code ticks (`code`)
            .replace(/\n\n/g, ' ')       // Replace double line breaks with space
            .replace(/\n-\s/g, ', ')     // Replace bullet points with commas
            .replace(/\n\d+\.\s/g, ', ') // Replace numbered lists with commas
            .replace(/\n/g, ' ')         // Replace remaining line breaks with spaces
            .replace(/\s{2,}/g, ' ')     // Replace multiple spaces with single space
            .trim();                     // Trim extra whitespace
    };


  const handleViewPastDiscussion = async () => {
    await fetchPastTasks();
    setIsPastTasksCodeModalOpen(true);
  };

  const handlePageChange = async (newPage) => {
    const newSkip = (newPage - 1) * limit;
    await fetchPastTasks(newSkip, limit);
  };

  const handleLimitChange = async (newLimit) => {
    await fetchPastTasks(0, newLimit);
  };

  const fetchRepositoryDetails = async () => {
    try {
      const response = await getRepository(projectId, details?.container_id);

      if (response.repository) {
        setRepository(response.repository);
      } else {
        setRepository(null);
      }
    } catch (err) {
      throw new Error('Failed to fetch repository details');
    }
  };

  useEffect(() => {
    if (details?.container_id && projectId) {
      fetchRepositoryDetails();
    }
  }, [details]);

  const fetchLowLevelDesignDetails = async () => {
    try {
      setLoading(true);
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_API_URL}/architecture/design_nodes/${architectureLeafId}`, { headers: getHeadersRaw() }
      );
      if (response.status === 200) {
        setDetails(
          Array.isArray(response.data) ? response.data[0] : response.data
        );
        setCurrentFramework(
          response.data[0]?.properties?.framework ? frameworks.find(f => f.key === response.data[0]?.properties?.framework) : frameworks[0]
        )

        setCurrentBranch(Array.isArray(response.data) ? response.data[0]?.properties?.branch : response.data?.properties?.branch)
        setCurrentPlatform(Array.isArray(response.data) && response.data[0]?.properties?.platform ? ({
          key: response.data[0].properties.platform,
          label: PLATFORMS.find(p => p.key === response.data[0]?.properties?.platform).label,
          icon: PLATFORMS.find(p => p.key === response.data[0]?.properties?.platform).icon
        })
          : ({ key: "generic", label: "Generic", icon: <Globe className="w-5 h-5" /> }));
      }
    } catch (error) {

      setError(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (architectureLeafId) fetchLowLevelDesignDetails();
  }, [architectureLeafId, searchParams, setDetails]);

  useEffect(() => {
    calculateDimensions()
  }, [calculateDimensions])

  useEffect(() => {
    if (details?.container_id && searchParams.get('showrepo') === 'true') {
      setShowRepoDetails(true);
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.delete('showrepo');
      router.replace(`${pathname}?${newSearchParams.toString()}`);
    }
  }, [details, searchParams, pathname, router]);

  const handleBack = () => {
    if (sessionStorage.getItem("querySet")) {
      const backTabs = Number(sessionStorage.getItem("querySet"));
      sessionStorage.removeItem("querySet");
      if (window.history.length > Number(backTabs) * (-1)) {
        window.history.go(backTabs);
      }
      else {
        router.push(buildProjectUrl(projectId, 'architecture/architecture-requirement'));
      }
    }
    else {
      router.back();
    }
  };

  if (error) {
    return (
      <ErrorView
        title="Unable to Load Design Details"
        message={en.UnableToLoadDesignDetails}
        onRetry={() => fetchLowLevelDesignDetails()}
        panelType="main"
      />
    );
  }

  const hasDetails = details && Object.keys(details).length > 0;

  const handleDesignAction = async (createDesign = false) => {
    const newSearchParams = new URLSearchParams(searchParams);
    if (createDesign) {
      showAlert("Creating Design", "info");
      setDesignCreating(true);
      try {
        let designNode = await createDesignNode(architectureLeafId);
        showAlert("Design Created", "success");
        newSearchParams.set("design_id", designNode?.id);
        router.push(`${pathname}?${newSearchParams.toString()}`);
        return true;
      } catch (error) {

      } finally {
        setDesignCreating(false);
      }
    }
    newSearchParams.set("discussion", "new");
    newSearchParams.set("node_id", details?.id);
    newSearchParams.set("node_type", "Design");
    newSearchParams.set("discussionType", "specification");
    updateSessionStorageBackHistory();
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };

  const handleViewPastDiscussionFunction = async () => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("view_past_discussions", "true");
    newSearchParams.set("node_id", details.id);
    newSearchParams.set("discussion_type", "specification");
    updateSessionStorageBackHistory();
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };


  const updateProps = {
    onUpdateClick: () => {
      let isNewDesign = handleDesignAction(hasDetails ? false : true);
    },
    buttonText: hasDetails ? "Update Design" : "Create Design",
    classNames: [designCreating ? "opacity-50 cursor-not-allowed" : ""],
    classNames: [designCreating ? "opacity-50 cursor-not-allowed" : ""],
    tooltip: hasDetails ? TOOLTIP_CONTENT.Architecture.design.update : TOOLTIP_CONTENT.Architecture.design.create,
  };

  const title =
    details?.properties?.Title === "New Design"
      ? lowLevelDesignVal?.[0]?.title
      : details?.properties?.Title || "Design Details";

  const handleRepoDetailsOpen = () => {
    setShowRepoDetails(true);
  };

  const handleRepoDetailsClose = () => {
    setShowRepoDetails(false);
  };

  const handleBranchUpdate = async (newBranch) => {
    try {
      await Promise.all([
        updateNodeByPriority(architectureLeafId, 'branch', newBranch),
        updateNodeByPriority(details.id, 'branch', newBranch)
      ]);
      setCurrentBranch(newBranch);
      showAlert('Branch updated successfully', 'success');
    } catch (error) {

      showAlert('Failed to update branch', 'error');
    }
  };

  const handleFrameworkChange = (newFramework) => {
    try {
      handlePropertyUpdate("framework", newFramework.key);
      setCurrentFramework(newFramework);
      showAlert("Framework updated successfully", "success");
    } catch (error) {

      showAlert("Failed to update framework", "error");
    }
  };

  const handleGenerateCode = (sessionData) => {
    setIsGeneratingCode(true);
  };
  useEffect(() => {
    if (searchParams.get("task_id")) {
    setCodeGenSetupModal(false);
    }

  },[searchParams])

  const BranchSelection = () => {
    return (
      <BranchSelector
        projectId={projectId}
        containerId={details.container_id}
        currentBranch={currentBranch}
        onUpdate={handleBranchUpdate}
        tooltip={"Select branch"}
        className="w-full"
      />
    )
  }

  const HeaderSection = () => {
    const { Type, container_id } = details?.properties || {};

    return (
      <div className="flex flex-col space-y-4 top-1" style={{ zIndex: 5 }}>
        <div className="flex flex-col border border-semantic-gray-200">
          <div className="relative px-2.5 py-1 space-y-2">
            <div className="flex items-center gap-3">
              <IconButton
                icon={<ArrowLeft className="w-5 h-5 text-semantic-gray-600" />}
                tooltip="Go back"
                onClick={handleBack}
                className="hover:bg-semantic-gray-100"
              />
              <div className="flex items-center gap-2">
                <h2 className="typography-body-lg font-weight-semibold text-semantic-gray-800">
                  {title || "Design Details"}
                </h2>
                {Type && (
                  <div className="flex items-center gap-1">
                    <Badge type={Type} />
                  </div>
                )}
              </div>
            </div>

            <div className="flex items-center justify-between pl-3 pb-1">
              <DynamicButton
                type="submit"
                variant="secondary"
                size="default"
                icon={Eye}
                onClick={() =>
                  router.push(
                    buildProjectUrl(projectId, `architecture/component/${architectureLeafId}`)
                  )
                }
                tooltip={TOOLTIP_CONTENT.Architecture.design.viewComponents}
                text="View component"
              />

              <div className="flex items-center gap-2">
                {showRepoDetails && (
                  <RepositoryDetailsModal
                    open={true}
                    projectId={projectId}
                    containerId={details.container_id}
                    onClose={handleRepoDetailsClose}
                    onSuccess={fetchLowLevelDesignDetails}
                    handleRepoChange={setRepository}
                  />
                )}

                <ConfigureButtons updateProps={updateProps} />
                  <button
                  onClick={handleViewPastDiscussionFunction}
                  className="min-w-[100px] flex items-center gap-1 px-3 py-2 bg-white border border-semantic-gray-300
                             rounded-md text-black hover:bg-semantic-gray-100 focus:ring-2 focus:ring-semantic-gray-500
                             focus:ring-offset-2 typography-body-sm"
                >
                  <BookOpen size={14} />
                  History
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };


  const DesignSpecificationSection = () => (
    <div className="">
      {details?.ui_metadata && (
        <PropertiesRenderer
          properties={details.properties}
          metadata={details.ui_metadata}
          to_skip={["Title", "Type"]}
          onUpdate={handlePropertyUpdate}
        />
      )}
    </div>
  );

  const UpdateButton = ({ label, section, nodeId, isCreation }) => {
    const handleUpdate = () => {
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.set("discussion", "new");
      newSearchParams.set("node_id", nodeId);
      newSearchParams.set("node_type", "Design");
      newSearchParams.set("discussionType", section);
      updateSessionStorageBackHistory();
      router.push(`${pathname}?${newSearchParams.toString()}`);
    };

    const Headtext = isCreation ? "Create" : "Update"

    return (
      <>
        <DynamicButton
          type="submit"
          variant="primary"
          size="default"
          icon={Settings}
          onClick={handleUpdate}
          text={`${Headtext} ${label}`}
        />
      </>
    );
  };


  const TestCaseCard = ({ testCase }) => {
    const getTypeIcon = (type) => {
      switch (type.toLowerCase()) {
        case 'positive':
          return <CheckCircle2 className="text-green-500 mr-2" />;
        case 'negative':
          return <AlertTriangle className="text-red-500 mr-2" />;
        default:
          return <Info className="text-primary mr-2" />;
      }
    };

    return (
      <div className="bg-white shadow-md rounded-lg border border-semantic-gray-200 p-5 mb-4 hover:shadow-lg transition-all duration-300">
        <div className="flex items-center mb-3">
          {getTypeIcon(testCase.Type)}
          <h4 className="typography-body-lg font-weight-semibold text-semantic-gray-800 truncate">
            {testCase.Title}
          </h4>
        </div>
        <div className="space-y-2 text-semantic-gray-700">
          <div className="flex items-start">
            <span className="font-weight-medium text-semantic-gray-600 w-36 shrink-0">
              Type:
            </span>
            <span className="text-semantic-gray-800">{testCase.Type}</span>
          </div>
          <div className="flex items-start">
            <span className="font-weight-medium text-semantic-gray-600 w-36 shrink-0">
              Description:
            </span>
            <span className="text-semantic-gray-800">{cleanDescription(testCase.Description)}</span>
          </div>
          <div className="flex items-start">
            <span className="font-weight-medium text-semantic-gray-600 w-36 shrink-0">
              Expected Result:
            </span>
            <span className="text-semantic-gray-800">{cleanDescription(testCase.ExpectedResult)}</span>
          </div>
        </div>
      </div>
    );
  };

  const TestCasesSection = ({ details }) => {
    const testSections = [
      {
        title: "Unit Test Cases",
        tests: details?.UnitTest,
        emptyMessage: "No Unit Test Cases found",
        id: "unit-test"
      },
      {
        title: "Integration Test Scenarios",
        tests: details?.IntegrationTest,
        emptyMessage: "No Integration Test Scenarios found",
        id: "integration-test"
      },
      {
        title: "Performance Test Cases",
        tests: details?.PerformanceTest,
        emptyMessage: "No Performance Test Cases found",
        id: "performance-test"
      },
      {
        title: "Fault Tolerance Test Cases",
        tests: details?.RobustnessTest,
        emptyMessage: "No Fault Tolerance Test Cases found",
        id: "robustness-test"
      }
    ];

    return (
      <div className="container mx-auto px-4 py-6">
        <div>
          {/* Header Section */}
          <div className="flex items-center justify-between mb-6 border-b pb-3">
            <h2 className="typography-heading-2 font-weight-bold text-semantic-gray-900">
              Design Test Cases
            </h2>
            <UpdateButton
              label="Test Cases"
              isCreation={
                !details?.UnitTest?.length &&
                !details?.IntegrationTest?.length &&
                !details?.PerformanceTest?.length &&
                !details?.RobustnessTest?.length
              }
              section="testcases"
              nodeId={details.id}
            />
          </div>

          {testSections.map((section) => (
            <div id={section.id} key={section.id} className="mb-8">
              <h3 className="typography-body-lg font-weight-semibold text-semantic-gray-800 mb-4 border-b pb-2">
                {section.title}
              </h3>
              {section.tests?.length ? (
                <div className="space-y-4">
                  {section.tests.map((testCase, index) => (
                    <TestCaseCard key={index} testCase={testCase} />
                  ))}
                </div>
              ) : (
                <div className="text-semantic-gray-500 italic text-center py-4">
                  {section.emptyMessage}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  };


  const BehaviorCard = ({ behavior }) => {
    const [expanded, setExpanded] = useState(false);

    const getIcon = (type) => {
      switch (type.toLowerCase()) {
        case 'algorithm':
          return <Code className="text-purple-500 mr-2" />;
        case 'state':
          return <Layers className="text-teal-500 mr-2" />;
        default:
          return <Braces className="text-indigo-500 mr-2" />;
      }
    };

    return (
      <div className="bg-white shadow-md rounded-lg overflow-hidden border border-semantic-gray-200 transition-all duration-300 hover:shadow-lg">
        <div
          className="flex justify-between items-center px-5 py-4 cursor-pointer hover:bg-semantic-gray-50"
          onClick={() => setExpanded(!expanded)}
        >
          <div className="flex items-center">
            {getIcon(behavior.Type)}
            <h4 className="typography-body-lg font-weight-semibold text-semantic-gray-800">{behavior.Title}</h4>
          </div>
          {expanded ? (
            <ChevronUp className="text-semantic-gray-500" size={20} />
          ) : (
            <ChevronDown className="text-semantic-gray-500" size={20} />
          )}
        </div>

        {expanded && (
          <div className="px-5 pb-4 space-y-3">
            <pre className="bg-semantic-gray-100 p-3 rounded-md overflow-x-auto typography-body-sm text-semantic-gray-800 border border-semantic-gray-200">
              {behavior.Details}
            </pre>
          </div>
        )}
      </div>
    );
  };

  const DesignBehaviorSection = ({ details }) => {
    const behaviorSections = [
      {
        title: 'Algorithmic Details',
        behaviors: details?.Algorithm,
        emptyMessage: 'No Algorithmic Details Available',
        id: 'algorithm',
      },
    ];

    return (
      <div className="container mx-auto px-4 py-6">
        <div>
          <div className="flex justify-between items-center mb-6 border-b pb-3">
            <h2 className="typography-heading-2 font-weight-bold text-semantic-gray-900">Design Behavior</h2>
            <UpdateButton
              label="Design Behavior"
              isCreation={!details?.Algorithm?.length && !details?.StateLogic?.length}
              section="behavior"
              nodeId={details.id}
            />
          </div>

          {behaviorSections.map((section, sectionIndex) => (
            <div id={section.id} key={sectionIndex} className="mb-8">
              <h3 className="typography-body-lg font-weight-semibold text-semantic-gray-800 mb-4 border-b pb-2">
                {section.title}
              </h3>

              {section.behaviors?.length ? (
                <div className="space-y-4">
                  {section.behaviors.map((behavior, behaviorIndex) => (
                    <BehaviorCard key={behaviorIndex} behavior={behavior} />
                  ))}
                </div>
              ) : (
                <div className="text-semantic-gray-500 italic text-center py-4">
                  {section.emptyMessage}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  };

  const DiagramCard = ({ diagram, type }) => {
    const getIcon = () => {
      switch (type) {
        case 'sequence':
          return <Share2 className="text-primary mr-2" />;
        case 'state':
          return <GitBranch className="text-green-500 mr-2" />;
        default:
          return <Workflow className="text-purple-500 mr-2" />;
      }
    };

    return (
      <div className="bg-white shadow-md rounded-lg p-5 mb-4 hover:shadow-lg transition-all duration-300">
        <div className="flex items-center mb-3">
          {getIcon()}
          <h4 className="typography-heading-4 text-semantic-gray-800">{diagram.Title}</h4>
        </div>

        {diagram.Description && (
          <p className="text-semantic-gray-600 mb-4">{diagram.Description}</p>
        )}

        <div className="bg-white rounded-md p-3 overflow-x-auto">
          <NoSSR chartDefinition={diagram.Diagram} />
        </div>
      </div>
    );
  };

  const ComponentInteractionsSection = ({ details }) => {
    const diagramSections = [
      {
        title: "Sequence Diagrams",
        diagrams: details?.Sequence,
        type: 'sequence',
        emptyMessage: "No Sequence Diagrams Available",
        id: "sequence"
      },
      {
        title: "State Machine Diagrams",
        diagrams: details?.StateDiagram,
        type: 'state',
        emptyMessage: "No State Machine Diagrams Available",
        id: "state-diagram"
      }
    ];

    return (
      <div className="container mx-auto pr-4 py-6">
        <div>
          <div className="flex justify-between items-center mb-6 border-b pb-3">
            <h2 className="typography-heading-2 font-weight-bold text-semantic-gray-900 flex items-center">
              <Workflow className="mr-3 text-purple-600" />
              Design Component Interactions
            </h2>
            <UpdateButton
              label="Component Interactions"
              isCreation={!details?.Sequence?.length && !details?.StateDiagram?.length}
              section="component_interactions"
              nodeId={details.id}
            />
          </div>

          {diagramSections.map((section, sectionIndex) => (
            <div id={section.id} key={sectionIndex} className="mb-8">
              <h3 className="typography-body-lg font-weight-semibold text-semantic-gray-800 mb-4 border-b pb-2">
                {section.title}
              </h3>

              {section.diagrams?.length ? (
                <div className="space-y-4">
                  {section.diagrams.map((diagram, diagramIndex) => (
                    <DiagramCard
                      key={diagramIndex}
                      diagram={diagram}
                      type={section.type}
                    />
                  ))}
                </div>
              ) : (
                <div className="text-semantic-gray-500 italic text-center py-4 bg-semantic-gray-50 rounded-md">
                  {section.emptyMessage}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  };

  const ClassDiagramCard = ({ diagram }) => {
    return (
      <div className="bg-white shadow-md rounded-lg p-5 mb-4 hover:shadow-lg transition-all duration-300">
        <div className="flex items-center mb-4">
          <Layers className="text-indigo-500 mr-3" />
          <h4 className="typography-heading-4 text-semantic-gray-800">{diagram.Title}</h4>
        </div>

        <div className="bg-white rounded-md p-3 overflow-x-auto">
          <NoSSR chartDefinition={diagram.Diagram} />
        </div>
      </div>
    );
  };

  const ClassDiagramsSection = ({ details }) => {
    return (
      <div className="container mx-auto pr-4 py-6">
        <div>
          <div className="flex justify-between items-center mb-6 border-b pb-3">
            <h2 className="typography-heading-2 font-weight-bold text-semantic-gray-900 flex items-center">
              <Database className="mr-3 text-indigo-600" />
              Design Class Diagrams
            </h2>
            <UpdateButton
              label="Class Diagrams"
              section="classdiagram"
              nodeId={details.id}
              isCreation={!details?.ClassDiagram?.length}
            />
          </div>

          {details?.ClassDiagram?.length ? (
            <div className="space-y-4" id="class-diagram">
              {details.ClassDiagram.map((diagram, index) => (
                <ClassDiagramCard
                  key={index}
                  diagram={diagram}
                />
              ))}
            </div>
          ) : (
            <div className="text-semantic-gray-500 italic text-center py-4 bg-semantic-gray-50 rounded-md">
              No Class Diagrams Available
            </div>
          )}
        </div>
      </div>
    );
  };

  const AdditionalDesignSection = () => {
    return (
      <div className="design-details-additional-design-section ">

        <DesignBehaviorSection details={details} />

        {/* <TestCasesSection details={details} /> */}

        <ComponentInteractionsSection details={details} />

        <ClassDiagramsSection details={details} />
      </div>
    );
  };

  const additionalFields = [
    'Algorithm',
    'UnitTest',
    'IntegrationTest',
    'PerformanceTest',
    'RobustnessTest',
    'Sequence',
    'StateDiagram',
    'ClassDiagram',
  ];

  const metadataEntries = Object.entries(details?.ui_metadata || {}).filter(
    ([key, value]) =>
      !["Title", "Type"].includes(key) &&
      value.hidden !== true &&
      details?.properties?.hasOwnProperty(key)
  )
    .map(([key, value]) => {
      const labelOrKey = value?.Label || key;
      const splitCamelCase = (str) => str.replace(/([a-z])([A-Z])/g, '$1 $2');

      return {
        id: `${splitCamelCase(labelOrKey).toLowerCase().replace(/[_\s]+/g, '-')}`,
        name: splitCamelCase(labelOrKey).replace(/[_-]/g, ' '),
        children: []
      };
    });

  const linkedFigmaEntry = details?.ui_metadata
    ? {
      id: 'linked-figma-elements',
      name: 'Linked Figma Elements',
      children: [],
    }
    : null;

  const additionalEntries = additionalFields
    .filter((field) => details?.[field])
    .map((field) => {
      const splitCamelCase = (str) => str.replace(/([a-z])([A-Z])/g, '$1 $2');
      return {
        id: `${splitCamelCase(field).toLowerCase().replace(/[_\s]+/g, '-')}`,
        name: splitCamelCase(field).replace(/[_-]/g, ' '),
        children: []
      };
    });

  const treeData = [
    ...metadataEntries,
    ...(linkedFigmaEntry ? [linkedFigmaEntry] : []),
    ...additionalEntries,
  ];

  const handleScrollToSection = (id) => {
    const element = document.getElementById(id);
    const mainContent = document.getElementById("main-content")
    if (element && mainContent) {
      const topOffset = element.offsetTop;
      mainContent.scrollTo({
        top: topOffset - 80,
        behavior: "smooth",
      });
    }
  };

    if (loading) {
      return <TwoColumnSkeletonLoader />;
    }

  return (
    <div className="relative flex max-h-[78vh] overflow-hidden bg-white-50" style={{
      height: contentHeight
    }}>
        <>
          <div>
            {treeData && (
              <NavigationTree
                treeData={treeData}
                handleScrollToSection={handleScrollToSection}
              />
            )}
          </div>
          <main
            id="main-content"
            className={`
          flex-1
          relative
          overflow-y-auto
          overflow-x-hidden
          transition-all
          duration-300
          ease-in-out
        `}
            style={{
              width: mainContentWidth,
            }}
          ><div className="w-full pl-4">
              <div className="mb-4">
                <HeaderSection />
              </div>
              {hasDetails ? (
                <div className="design-details-content-sub-wrapper mt-2">
                  <DesignSpecificationSection />
                  <FigmaSection
                    designNode={details}
                    projectId={projectId}
                    onUpdate={handleFigmaUpdate}
                  />
                  <AdditionalDesignSection />
                </div>
              ) : (
                <p className="design-details-not-found">
                  <EmptyStateView type="designDetails" />
                </p>
              )}
            </div>
          </main>
        </>

      {isGeneratingCode && (
        <CodeGenerationHandler
          projectId={projectId}
          itemId={architectureLeafId}
          onComplete={() => {
            setIsGeneratingCode(false);
          }}
        />
      )}
      {isVisible && <CodeGenerationModal />}
      {isPastTasksCodeModalOpen && (
        <PastTasksModal
          isOpen={isPastTasksCodeModalOpen}
          onClose={() => setIsPastTasksCodeModalOpen(false)}
          tasks={pastTasks}
          totalCount={totalCount}
          limit={limit}
          skip={skip}
          onPageChange={handlePageChange}
          onLimitChange={handleLimitChange}
          isLoading={isPastTaskLoading}
        />
      )}
      {codeGenSetupModal && (
        <CodeGenerationSetupModal
          isGeneratingCode={isGeneratingCode}
          onClose={() => setCodeGenSetupModal(false)}
          BranchSelection={BranchSelection}
          currentPlatform={currentPlatform}
          onPlatformChange={handlePlatformChange}
          onConfirm={handleGenerateCode}
          repository={repository}
          currentBranch={currentBranch}
          currentFramework={currentFramework}
          onFrameworkChange={handleFrameworkChange}
          projectId={projectId}
          containerId={details?.container_id}
          handleRepoChange={(repo) => {
            setRepository(repo);
            showAlert('Repository configured successfully', 'success');
            // Close the repository modal if it's open
            setShowRepoDetails(false);
          }}
        />
      )}
    </div>
  );
};

