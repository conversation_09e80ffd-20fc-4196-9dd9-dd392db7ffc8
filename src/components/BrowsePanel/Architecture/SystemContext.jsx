"use client"

import React, { useEffect, useState, useContext } from "react";
import "@/styles/tabs/architecture/systemContext.css"
import {
  useRouter,
  usePathname,
  useSearchParams
} from "next/navigation";
import PropertiesRenderer from "@/components/UiMetadata/PropertiesRenderer";
import { fetchSystemContextWithContainers,getReconfigNodeStatus } from "@/utils/api";
import TableComponent from "@/components/SimpleTable/ArchitectureTable"
import { ArchitectureContext } from "@/components/Context/ArchitectureContext";
import ConfigureButtons from "@/components/ConfigureButtons/ConfigureButtons";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import Badge from "@/components/UIComponents/Badge/Badge"
import en from "@/en.json"
import { BookOpen, Settings } from 'lucide-react';
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";

import ErrorView from "@/components/Modal/ErrorViewModal";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import { updateNodeByPriority } from "@/utils/api";
import { updateSessionStorageBackHistory } from "@/utils/helpers";
import NavigationTree from "@/components/Modal/NavigationTreeComponent"
import { TwoColumnSkeletonLoader } from "@/components/UIComponents/Loaders/LoaderGroup"
import { useResponsiveDimensions } from "@/utils/responsiveness";
import { TOOLTIP_CONTENT } from "@/utils/constant/tooltip";
import RequirementsBanner from "@/components/UIComponents/Badge/RequirementBanner";

const SystemContext = () => {
  const [loading, setLoading] = useState(true);
  const [systemContext, setSystemContext] = useState(null);
  const [error, setError] = useState(null)
  const { setContainerData } = useContext(ArchitectureContext);
  const { showAlert } = useContext(AlertContext);
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const projectId = pathname.split("/")[3];
  const [showBaner,setShowBaner] = useState(false)
  const router = useRouter();
  const { mainContentWidth, contentHeight, calculateDimensions } = useResponsiveDimensions();



  const fetchData = async () => {
    setLoading(true);
    try {
      const data = await fetchSystemContextWithContainers(projectId);

      setSystemContext(data);
      const reconfig = await getReconfigNodeStatus(projectId)
      let showBanner = false;

      if (reconfig) {
        const hasReconfigNeeded =
        reconfig.SystemContext?.some(item => item.reconfig_needed === true)

        showBanner = hasReconfigNeeded;
      }
      setShowBaner(showBanner)

      // Only set container data if it exists
      if (data?.data?.containers) {
        setContainerData(data.data.containers);
      }
    } catch (error) {

      setError(error)
    } finally {
      setLoading(false);
    }
  };


  useEffect(() => {
    fetchData();
  }, [projectId, setContainerData]);


  useEffect(() => {
    calculateDimensions()
  }, [calculateDimensions])

  const headers = [
    { key: 'id', label: 'Id' },
    { key: 'title', label: 'Title' },
    { key: 'container_type', label: 'Container Type' },
    { key: 'description', label: 'Description' },
  ];



  if (loading) {
    return <TwoColumnSkeletonLoader />;
  }

  if (error) {
    return (
      <ErrorView
        title="Unable to Load API Documentations"
        message={en.UnableToLoadSystemContext}
        onRetry={() => fetchData()}
        panelType='main'
      />
    );
  }

  const handleRowClick = (Id) => {
    router.push(
      buildProjectUrl(projectId, `architecture/container/${Id}`)
    );
  };

  const handlePropertyUpdate = async (key, value) => {
    try {
      const systemContextId = systemContext?.data?.systemContext?.id;
      if (!systemContextId) {
        throw new Error('System Context ID not found');
      }

      const response = await updateNodeByPriority(systemContextId, key, value);

      if (response === "success") {
        // Update local state
        setSystemContext(prev => ({
          ...prev,
          data: {
            ...prev.data,
            systemContext: {
              ...prev.data.systemContext,
              properties: {
                ...prev.data.systemContext.properties,
                [key]: value
              }
            }
          }
        }));

        showAlert("Content updated successfully", "success");
      } else {
        throw new Error('Update failed');
      }
    } catch (error) {

      showAlert("Failed to update content", "error");
    }
  };

  const handleUpdateSystemContext = () => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("discussion", "new");
    newSearchParams.set("node_id", systemContext?.data?.systemContext?.id);
    newSearchParams.set("node_type", "SystemContext");
    newSearchParams.set("discussionType", "system_context_containers");
    updateSessionStorageBackHistory();
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };

  const handleUpdateSystemArchitecture = () => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("discussion", "new");
    newSearchParams.set("node_id", systemContext?.data?.systemContext?.id);
    newSearchParams.set("node_type", "SystemContext");
    newSearchParams.set("discussionType", "system_context_overview");
    updateSessionStorageBackHistory();
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };

  const updateProps = {
    onUpdateClick: handleUpdateSystemContext,
    buttonText: (systemContext?.data && systemContext?.data?.containers?.length > 0) ? "Update System Containers" : "Create System Containers",
    tooltip: (systemContext?.data && systemContext?.data?.containers?.length > 0) ? TOOLTIP_CONTENT.Architecture.system_context.update : TOOLTIP_CONTENT.Architecture.system_context.create
  };

  const handleViewPastDiscussion = async () => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("view_past_discussions", "true");
    newSearchParams.set("node_id", systemContext?.data?.systemContext?.id);
    newSearchParams.set("discussion_type", "SystemContext");
    updateSessionStorageBackHistory();
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };

  const treeData = [...Object.entries(systemContext.model?.SystemContext?.ui_metadata || {})
    .filter(
      ([key, value]) =>
        !["Title", "Type"].includes(key) && value.hidden !== true &&
        systemContext.data.systemContext.properties.hasOwnProperty(key)
    )
    .map(([key, value]) => ({
      id: `${(value.Label || key).toLowerCase().replace(/[_\s]+/g, '-')}`,
      name: (value.Label || key).replace(/[_-]/g, ' '), // Use Label if available, fallback to key
      children: [], // Add children if hierarchical data exists
    })),
  ...(systemContext.data?.containers?.length > 0
    ? [{
      id: "system-containers",
      name: "System Containers",
      children: [],
    }]
    : []),]

  const handleScrollToSection = (id) => {

    const element = document.getElementById(id);

    const mainContent = document.getElementById("main-content")

    if (element && mainContent) {
      const topOffset = element.offsetTop;
      mainContent.scrollTo({
        top: topOffset - 80, // Account for header
        behavior: "smooth",
      });
    }
  };




  const HeaderSection = () => {
    const { Title, Type } = systemContext?.data?.systemContext?.properties || {};

    return (
      <div className="flex flex-col space-y-4 top-1" style={{ zIndex: 5 }}>
         {showBaner? (
          <RequirementsBanner value={"System Context"}/>
        ):null}
        <div className="flex flex-col border border-semantic-gray-200">
          <div className="relative px-2 py-1 space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-2">
                  <h2 className="typography-body-lg font-weight-semibold text-semantic-gray-800">
                    {Title || "System Context"}
                  </h2>
                  {Type && (
                    <div className="flex items-center gap-1">
                      <Badge type={Type} />
                    </div>
                  )}
                </div>
              </div>
              <div className="flex items-center gap-2">
                <DynamicButton
                  type="submit"
                  variant="primary"
                  size="default"
                  icon={Settings}
                  onClick={handleUpdateSystemArchitecture}
                  text="Update System Overview"
                />
                <ConfigureButtons updateProps={updateProps} />
                <button
                  onClick={handleViewPastDiscussion}
                  className="min-w-[100px] flex items-center gap-1 px-3 py-2 bg-white border border-semantic-gray-300
                             rounded-md text-black hover:bg-semantic-gray-100 focus:ring-2 focus:ring-semantic-gray-500
                             focus:ring-offset-2 typography-body-sm"
                >
                  <BookOpen size={14} />
                  History
                </button>
              </div>
            </div>
            <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-primary-100/50 via-primary-300/20 to-transparent"></div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="relative flex max-h-[78vh] overflow-hidden bg-white-50" style={{
      height: contentHeight
    }}>
      {systemContext?.data ? (
        <>
          <div>
            {treeData && (
              <NavigationTree
                treeData={treeData}
                handleScrollToSection={handleScrollToSection}
              />
            )}
          </div>

          <main
            id="main-content"
            className={`
              flex-1
              relative
              overflow-y-auto
              overflow-x-hidden
              transition-all
              duration-300
              ease-in-out
            `}
            style={{
              width: mainContentWidth,
            }}
          >
            <div className="w-full pl-3 pr-4">
              <div className="mb-4">
                <HeaderSection />
              </div>

              <PropertiesRenderer
                properties={systemContext.data.systemContext.properties}
                metadata={systemContext.model?.SystemContext?.ui_metadata}
                to_skip={["configuration_state", "Type", "Title", "type"]}
                onUpdate={handlePropertyUpdate}
              />

              <div id="system-containers" className="relatedComponentDiv">
                {systemContext.data.containers ? (
                  <TableComponent
                    data={systemContext.data.containers.map(data => ({
                      id: data.id,
                      title: data.properties.Title,
                      container_type: data.properties.ContainerType,
                      description: data.properties.Description
                    }))}
                    onRowClick={handleRowClick}
                    headers={headers}
                    sortableColumns={{ "id": true, "title": true, "container_type": true }}
                    itemsPerPage={20}
                    title={en.ChildContainersHeading}
                  />
                ) : (
                  <></>
                )}
              </div>
            </div>
          </main>
        </>
      ) : (
        <div className="system-context-empty-state">
          <EmptyStateView type="systemContext" className="w-full" />
        </div>
      )}
    </div>

  );
};

export default SystemContext;