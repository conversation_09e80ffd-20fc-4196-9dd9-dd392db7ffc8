import React from "react";
import Image from "next/image";
import dottedImage from "../../../public/images/dotted.png";

const Dropdown = ({ isDropdownOpen, toggleDropdown, dropdownRef, Data }) => {
  return (
    <div className="relative" ref={dropdownRef}>
      <button onClick={toggleDropdown} className="shadow-lg p-2 bg-[#f5f6f7] border-semantic-gray-300">
        <Image src={dottedImage} className="w-6" alt="dotted icons" />
      </button>
      {isDropdownOpen && (
        <div className="absolute right-0 mt-2 w-72 bg-white border border-semantic-gray-200 rounded-md shadow-lg z-50">
          {Data.map((item, index) => (
            <div key={index} className="px-4 py-2 hover:bg-semantic-gray-100 text-bold text-color cursor-pointer">
              {item}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default Dropdown;
