// AttachmentButton.js
import React from 'react';

const AttachmentButton = React.forwardRef(({ onClick, fileCount, acceptedTypes }, ref) => (
  <div className="relative group">
    <button
      type="button"
      onClick={(e) => {
        e.preventDefault();
        e.stopPropagation();
        onClick(e);
      }}
      className="ml-2 p-2 text-semantic-gray-500 hover:text-semantic-gray-700 focus:outline-none relative"
      ref={ref}
    >
      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
      </svg>
      {fileCount > 0 && (
        <span className="absolute -top-1 -right-1 bg-orange-500 text-white typography-caption rounded-full w-4 h-4 flex items-center justify-center">
          {fileCount}
        </span>
      )}
      {acceptedTypes && (<span className="ml-2 typography-caption text-semantic-gray-500">({acceptedTypes})</span>)}
    </button>
    <div className="absolute bottom-full -left-1 transform -translate-x-1/2 mb-2 px-3 py-1 bg-semantic-gray-800 text-white typography-caption rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">
      Attach images, text or PDFs
    </div>
  </div>
));

AttachmentButton.displayName = "AttachmentButton"

export default AttachmentButton;