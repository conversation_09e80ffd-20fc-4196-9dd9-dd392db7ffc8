import React from 'react';
import ApprovalComments from "@/components/Modal/ApprovalComments";
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import { useState, useCallback } from 'react';

function ModificationActionButtons({
  modificationFeedback,
  modificationStatus,
  allUsers,
  showAlert,
  setIsApproversListModalOpen,
  userId,
  setModifications,
  discussionId,
  index,
  mergeModificationRequest,
  modifications,
  handleAddComment,
  handleClose
}) {
  const [isChatModalOpen, setIsChatModalOpen] = useState(false);


  const handleOpenChat = useCallback(() => {
    setIsChatModalOpen(true);
  }, []);
  
  const handleCloseChat = useCallback(() => {
    setIsChatModalOpen(false);
  }, []);
  return (
    <div className="flex space-x-2">
      {/* TODO: Uncomment later */}
      {/* {(!modificationFeedback || modificationStatus == "idle") && (
        <DynamicButton
          variant="primary"
          icon=""
          text="Ask for Feedback"
          onClick={() => {
            if (allUsers.length === 0) {
              showAlert("No users are part of the discussion", "warning");
            } else {
              setIsApproversListModalOpen(true);
            }
          }}
          tooltip='Ask feedback for the assigned user in discussion'
          placement='bottom'
        />
      )} */}
      {modificationStatus == "pending" ? (
        modificationFeedback?.approver_id == userId ? (
          <>
            <DynamicButton
              variant="success"
              icon=""
              text=" Approve"
              onClick={async () => {
                setModifications((prevModifications) => {
                  const updatedModifications = [...prevModifications];
                  updatedModifications[index].modification_feedback = {
                    status: "Merging in progress",
                  };
                  return updatedModifications;
                });
                await mergeModificationRequest(discussionId, index, "approve");
                setModifications((prevModifications) => {
                  const updatedModifications = [...prevModifications];
                  updatedModifications[index].modification_feedback = {
                    status: "approved",
                  };
                  return updatedModifications;
                });
                showAlert("Modification approved successfully", "success");
              }}
            />
            <DynamicButton
              variant="danger"
              icon=""
              text=" Reject"
              onClick={async () => {
                setModifications((prevModifications) => {
                  const updatedModifications = [...prevModifications];
                  updatedModifications[index].modification_feedback = {
                    status: "rejection pending",
                  };
                  return updatedModifications;
                });
                await mergeModificationRequest(discussionId, index, "reject");
                setModifications((prevModifications) => {
                  const updatedModifications = [...prevModifications];
                  updatedModifications[index].modification_feedback = {
                    status: "rejected",
                  };
                  return updatedModifications;
                });
                showAlert("Modification rejected successfully", "success");
              }}
            />
          </>
        ) : (
          <DynamicButton
            variant="ghost"
            icon=""
            text=" Approval Pending"
            disabled={true}
          />
        )
      ) : (
        <button
          className={`px-4 py-2 bg-semantic-gray-200 text-semantic-gray-600 rounded-md hover:bg-semantic-gray-300 ${modificationFeedback?.status == "idle" || !modificationFeedback
            ? "hidden"
            : ""
            }`}
          disabled
        >
          {modificationStatus.charAt(0).toUpperCase() + modificationStatus.slice(1)}
        </button>
      )}
      {/* TODO: Uncomment later */}
      {/* <BootstrapTooltip title="Add your comment here!" placement="bottom">
        <button
          className="p-2 rounded-full hover:bg-semantic-gray-100"
          onClick={() => setIsChatModalOpen(true)}
        >
          <FaComment className="text-semantic-gray-600" />
        </button>
      </BootstrapTooltip> */}
      <ApprovalComments
        title={modifications[index].modified_node.Title}
        discussionId={discussionId}
        modificationIndex={index}
        description={modifications[index].modified_node.Description}
        onAddUser={(updatedUsers) => {}}
        onAddComment={handleAddComment}
        onClose={handleOpenChat}
        setIsOpen={handleCloseChat}
        isOpen={isChatModalOpen}
      />
    </div>
  );
}

export default ModificationActionButtons;