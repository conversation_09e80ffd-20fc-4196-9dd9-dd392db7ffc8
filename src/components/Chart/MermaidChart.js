/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useRef, useState, useContext } from "react";
import dynamic from "next/dynamic";
import { ArchitectureContext } from "../Context/ArchitectureContext";
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { fetchArchitecturalElementByProperty } from "@/utils/api";
import { FiPlus, FiMinus, FiMaximize, FiMinimize, FiDownload } from 'react-icons/fi';
import * as Dialog from '@radix-ui/react-dialog';

const mermaid = dynamic(() => import("mermaid"), { ssr: false });
// Fixed svgPanZoom import to ensure the default export is used
const svgPanZoom = dynamic(() => import("svg-pan-zoom").then(mod => mod.default), { ssr: false });

const MermaidChart = ({ chartDefinition }) => {
  const id = `mermaid-${Math.floor(1000 + Math.random() * 9000)}`;
  const mermaidRef = useRef(null);
  const modalMermaidRef = useRef(null);
  const panZoomRef = useRef(null);
  const modalPanZoomRef = useRef(null);
  const modalContentRef = useRef(null);
  const [_isFullscreen, setIsFullscreen] = useState(false);
  const [diagramHeight, setDiagramHeight] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const { getArchitecture, selectArchitecture, getArchitectureVal } = useContext(ArchitectureContext);
  const router = useRouter();
  const params = useParams();
  const [isZoomEnabled, setIsZoomEnabled] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);

  // Function to get computed CSS variable values
  const getCSSVariableValue = (variableName) => {
    if (typeof window === 'undefined') return '#000000'; // Default for SSR

    const root = document.documentElement;
    const value = getComputedStyle(root).getPropertyValue(variableName).trim();

    if (value) {
      // Convert HSL values to proper HSL format
      if (value.includes(' ')) {
        return `hsl(${value})`;
      }
      return value;
    }

    // Fallback colors based on the CSS variables from globals.scss
    const fallbacks = {
      '--primary': 'hsl(15 90% 50%)',
      '--foreground': 'hsl(222.2 84% 4.9%)',
      '--background': 'hsl(255 100% 100%)',
      '--semantic-gray-50': 'hsl(210 40% 98%)',
      '--semantic-gray-100': 'hsl(210 40% 96%)',
      '--semantic-gray-300': 'hsl(213 27% 84%)',
      '--semantic-gray-600': 'hsl(215 19% 35%)'
    };

    return fallbacks[variableName] || '#000000';
  };

  useEffect(() => {
    import("mermaid").then((mermaid) => {
      mermaid.default.initialize({
        startOnLoad: false,
        theme: "default",
        securityLevel: "loose",
        class: {
          titleTopMargin: 20,
          marginX: 50,
          marginY: 40,
          useMaxWidth: true,
          defaultDiagramDirection: 'TB',
          htmlLabels: true,
          diagramPadding: 20,
          curve: 'basis',
          arrowMarkerAbsolute: false,
          fontSize: '14px',
          labelBackground: '#f8f9fa',
          fontFamily: 'Inter, sans-serif'
        },
        flowchart: {
          useMaxWidth: true,
          htmlLabels: true,
          curve: 'basis',
          direction: 'LR',
          rankSpacing: 50,
          nodeSpacing: 8,
          padding: 15,
          ranker: 'tight-tree'
        },
        themeVariables: {
          primaryColor: getCSSVariableValue('--primary'),
          primaryTextColor: getCSSVariableValue('--foreground'),
          primaryBorderColor: getCSSVariableValue('--semantic-gray-600'),
          lineColor: getCSSVariableValue('--semantic-gray-600'),
          secondaryColor: getCSSVariableValue('--semantic-gray-100'),
          tertiaryColor: getCSSVariableValue('--background'),
          nodeBorder: getCSSVariableValue('--primary'),
          clusterBkg: getCSSVariableValue('--semantic-gray-50'),
          clusterBorder: getCSSVariableValue('--semantic-gray-300'),
          defaultLinkColor: getCSSVariableValue('--semantic-gray-600'),
          classText: getCSSVariableValue('--foreground'),
          titleColor: getCSSVariableValue('--foreground'),
          edgeLabel: getCSSVariableValue('--semantic-gray-600'),
          edgeColor: getCSSVariableValue('--semantic-gray-600'),
          fontSize: '14px',
          nodePadding: 8
        }
      })
    });
  }, []);

  let projectId = params.projectId;

  const renderMermaidDiagram = async (container, content) => {
    try {
      setIsLoading(true);
      setError(null);

      // First try parsing to catch syntax errors
      await import("mermaid").then((mermaid) => mermaid.default.parse(content)); //avoiding ssr issue as importing mermaid in the beginning will want to use window, that is only client side

      if (container.current) {
        // Clean up previous rendering
        container.current.removeAttribute('data-processed');

        // Render the diagram
        const diagram = await import("mermaid").then((mermaid) => mermaid.default.render(id, content));
        container.current.innerHTML = diagram.svg;

        const svgElement = container.current.querySelector('svg');
        if (svgElement) {
          svgElement.style.width = '100%';
          svgElement.style.height = '100%';
          svgElement.style.maxWidth = '100%';
          svgElement.style.maxHeight = '100%';

          // Calculate natural height for non-expanded view
          const naturalHeight = svgElement.getBoundingClientRect().height;
          if (!isExpanded && typeof window !== "undefined") {
            setDiagramHeight(Math.min(window.innerHeight * 0.45, Math.max(350, naturalHeight)));
          }

          attachNodeClickHandlers(svgElement);
        }
      }
      return true;
    } catch (err) {

      setError(err.message || 'Failed to render diagram');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Fixed to properly initialize pan-zoom functionality
  const initializePanZoom = async () => {
    if (mermaidRef.current) {
      const svgElement = mermaidRef.current.querySelector('svg');
      if (svgElement) {
        if (panZoomRef.current) {
          panZoomRef.current.destroy();
        }

        try {
          // Ensure the module is loaded
          const panZoomModule = await import('svg-pan-zoom');
          panZoomRef.current = panZoomModule.default(svgElement, {
            zoomEnabled: true,
            controlIconsEnabled: false,
            mouseWheelZoomEnabled: true,
            fit: true,
            center: true,
            minZoom: 0.1,
            maxZoom: 10,
            beforePan: function (_oldPan, newPan) {
              return newPan;
            }
          });
        } catch (err) {

        }
      }
    }
  };

  const handleDownload = () => {
    if (mermaidRef.current) {
      const svg = mermaidRef.current.querySelector('svg');
      if (svg) {
        // Clone the SVG to modify it without affecting the original
        const svgClone = svg.cloneNode(true);
        
        // Enhance text readability in the clone
        const textElements = svgClone.querySelectorAll('text');
        textElements.forEach(text => {
          // Increase font size for better readability
          const currentSize = parseInt(text.style.fontSize || text.getAttribute('font-size') || '12');
          text.style.fontSize = `${Math.max(currentSize + 2, 14)}px`;
          text.style.fontFamily = 'Arial, sans-serif';
          text.style.fontWeight = '600'; // Bolder text
          text.style.fill = '#1a202c'; // Darker color for better contrast
        });
        
        // Enhance title text even more
        const titleElements = svgClone.querySelectorAll('text[font-size="16"], .title-text');
        titleElements.forEach(title => {
          title.style.fontSize = '20px';
          title.style.fontWeight = 'bold';
          title.style.fill = '#000000';
        });
        
        // Improve shape borders for better definition
        const shapes = svgClone.querySelectorAll('rect, path, circle');
        shapes.forEach(shape => {
          shape.style.strokeWidth = '2px';
          if (shape.style.stroke === '' || !shape.style.stroke) {
            shape.style.stroke = 'hsl(var(--semantic-gray-700))';
          }
        });
  
        const serializer = new XMLSerializer();
        let source = serializer.serializeToString(svgClone);
  
        // Ensure XML namespace
        if (!source.includes('xmlns="http://www.w3.org/2000/svg"')) {
          source = source.replace(
            /^<svg/,
            '<svg xmlns="http://www.w3.org/2000/svg"'
          );
        }
  
        // Get SVG dimensions with fallbacks
        const viewBox = svg.viewBox.baseVal;
        const width = viewBox.width || svg.width.baseVal.value || 1000;
        const height = viewBox.height || svg.height.baseVal.value || 700;
  
        // Encode SVG string
        const svg64 = btoa(unescape(encodeURIComponent(source)));
        const image64 = `data:image/svg+xml;base64,${svg64}`;
  
        const img = new Image();
        img.onload = () => {
          const scaleFactor = 4; // Higher scale for crisp text
  
          const canvas = document.createElement('canvas');
          canvas.width = width * scaleFactor;
          canvas.height = height * scaleFactor;
  
          const ctx = canvas.getContext('2d');
          
          // Enable high-quality rendering
          ctx.textRenderingOptimization = 'optimizeQuality';
          ctx.imageSmoothingEnabled = true;
          ctx.imageSmoothingQuality = 'high';
  
          // Fill white background for better contrast
          ctx.fillStyle = '#ffffff';
          ctx.fillRect(0, 0, canvas.width, canvas.height);
  
          // Scale context for high resolution
          ctx.scale(scaleFactor, scaleFactor);
          ctx.drawImage(img, 0, 0, width, height);
  
          // Export as PNG for better text quality
          const pngData = canvas.toDataURL('image/png', 1.0);
  
          const link = document.createElement('a');
          link.href = pngData;
          link.download = 'system_context_diagram_hq.png';
          document.body.appendChild(link);
          link.click();
  
          // Cleanup
          document.body.removeChild(link);
          canvas.remove();
          img.src = '';
        };
  
        img.onerror = () => {
          console.error("Image failed to load from SVG data.");
        };
  
        img.src = image64;
      } else {
        console.error("SVG element not found inside mermaidRef");
      }
    }
  };

  // Fixed to properly initialize modal pan-zoom functionality
  const initializeModalPanZoom = async () => {
    if (modalMermaidRef.current) {
      const svgElement = modalMermaidRef.current.querySelector('svg');
      if (svgElement) {
        if (modalPanZoomRef.current) {
          modalPanZoomRef.current.destroy();
        }

        try {
          // Ensure the module is loaded
          const panZoomModule = await import('svg-pan-zoom');
          modalPanZoomRef.current = panZoomModule.default(svgElement, {
            zoomEnabled: true,
            controlIconsEnabled: false,
            mouseWheelZoomEnabled: true,
            fit: true,
            center: true,
          });
        } catch (err) {

        }
      }
    }
  };

  const handleNodeClick = (e) => {
    if (!isZoomEnabled) {
      e.stopPropagation();
      return;
    }
    const node = e.currentTarget;

    if (getArchitecture(node.textContent)) {
      const id = getArchitecture(node.textContent).id;
      selectArchitecture(getArchitectureVal(id, projectId));
      const { buildProjectUrl } = require('@/utils/navigationHelpers');
      const currentPath = window.location.pathname;
      const pathParts = currentPath.split('/');
      const organizationId = pathParts[1];
      const type = pathParts[2];
      router.push(buildProjectUrl(projectId, `architecture/high-level/${getArchitecture(node.textContent).id}`, type, organizationId));
    } else {
      if (params.architectureId) {
        fetchArchitecturalElementByProperty(params.architectureId, "Title", node.textContent).then((res) => {
          if (res.length > 0) {
            selectArchitecture({ ...res[0].properties, id: res[0].id });
            const { buildProjectUrl } = require('@/utils/navigationHelpers');
            const currentPath = window.location.pathname;
            const pathParts = currentPath.split('/');
            const organizationId = pathParts[1];
            const type = pathParts[2];
            router.push(buildProjectUrl(projectId, `architecture/high-level/${res[0].id}`, type, organizationId));
          }
        });
      }
    }
  };

  const attachNodeClickHandlers = (svgElement) => {
    const nodes = svgElement.querySelectorAll('.node');
    nodes.forEach(node => {
      node.style.cursor = 'pointer';
      node.addEventListener('click', handleNodeClick);
    });
  };

  // Improved zoom handlers with better error handling
  const handleZoomIn = (e, pzRef) => {
    e.stopPropagation();
    if (pzRef.current) {
      try {
        pzRef.current.zoomIn();
      } catch (err) {

      }
    }
  };

  const handleZoomOut = (e, pzRef) => {
    e.stopPropagation();
    if (pzRef.current) {
      try {
        pzRef.current.zoomOut();
      } catch (err) {

      }
    }
  };

  const handleReset = (e, pzRef) => {
    e.stopPropagation();
    if (pzRef.current) {
      try {
        pzRef.current.reset();
      } catch (err) {

      }
    }
  };

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
    if (!isExpanded) {
      setTimeout(() => {
        if (modalPanZoomRef.current) {
          modalPanZoomRef.current.reset();
          modalPanZoomRef.current.fit();
          modalPanZoomRef.current.center();
        }
      }, 600);
    }
  };

  // Fixed useEffect for better rendering and zoom initialization
  useEffect(() => {
    if (chartDefinition) {
      const timer = setTimeout(() => {
        renderMermaidDiagram(mermaidRef, chartDefinition).then((success) => {
          if (success && isZoomEnabled) {
            initializePanZoom();
          }
        });
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [chartDefinition, params.architectureId, projectId, isZoomEnabled]);

  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);

  // Fixed useEffect for modal rendering and zoom initialization
  useEffect(() => {
    if (isExpanded && chartDefinition) {
      const timer = setTimeout(() => {
        renderMermaidDiagram(modalMermaidRef, chartDefinition).then((success) => {
          if (success) {
            initializeModalPanZoom();
          }
        });
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [isExpanded, chartDefinition]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalContentRef.current && !modalContentRef.current.contains(event.target)) {
        setIsExpanded(false);
      }
    };

    if (isExpanded) {
      document.addEventListener("mousedown", handleClickOutside, true);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside, true);
    };
  }, [isExpanded]);

  return (
    <div className="relative">
      <div className="bg-white rounded-md w-full">
        <div className="p-4 flex justify-between items-center border-b" style={{ placeContent: "flex-end" }}>
          <div className="flex justify-between w-full">
            <button onClick={() => setIsZoomEnabled(!isZoomEnabled)} className="bg-semantic-gray-200 px-2 py-1 typography-body-sm rounded">
              {isZoomEnabled ? 'Disable zoom' : 'Enable zoom'}
            </button>
            <div className="flex space-x-2">
              {isZoomEnabled && (
                <>
                  <button onClick={(e) => handleZoomIn(e, panZoomRef)} className="p-1 rounded hover:bg-semantic-gray-100">
                    <FiPlus />
                  </button>
                  <button onClick={(e) => handleReset(e, panZoomRef)} className="p-1 rounded hover:bg-semantic-gray-100">
                    Reset
                  </button>
                  <button onClick={(e) => handleZoomOut(e, panZoomRef)} className="p-1 rounded hover:bg-semantic-gray-100">
                    <FiMinus />
                  </button>
                </>
              )}
              <button onClick={toggleExpand} className="p-1 rounded hover:bg-semantic-gray-100">
                {isExpanded ? <FiMinimize /> : <FiMaximize />}
              </button>
            </div>

            <Dialog.Root open={isExpanded} onOpenChange={setIsExpanded}>
              <Dialog.Portal>
                <Dialog.Overlay className="fixed inset-0 bg-black bg-opacity-50 z-50 radix-dialog-overlay" />
                <Dialog.Content className="fixed inset-0 flex items-center justify-center p-4 z-50 cursor-grab active:cursor-grabbing">
                  <div className="bg-white rounded-lg shadow-lg w-[90%] h-[90%] p-4 flex flex-col" ref={modalContentRef}>
                    <div className="flex justify-end items-center border-b mb-4 space-x-2">
                      <button onClick={(e) => handleZoomIn(e, modalPanZoomRef)} className="p-1 rounded hover:bg-semantic-gray-100">
                        <FiPlus />
                      </button>
                      <button onClick={(e) => handleReset(e, modalPanZoomRef)} className="p-1 rounded hover:bg-semantic-gray-100">
                        Reset
                      </button>
                      <button onClick={(e) => handleZoomOut(e, modalPanZoomRef)} className="p-1 rounded hover:bg-semantic-gray-100">
                        <FiMinus />
                      </button>

                      <button onClick={(e) => {
                        e.stopPropagation();
                        handleReset(e, panZoomRef)
                        handleDownload()
                      }} className="p-1 rounded hover:bg-semantic-gray-100">
                        <FiDownload />
                      </button>

                      <Dialog.Close asChild>
                        <button onClick={(e) => { e.stopPropagation(); toggleExpand(); }} className="p-1 rounded hover:bg-semantic-gray-100">
                          <FiMinimize />
                        </button>
                      </Dialog.Close>
                    </div>
                    <div className="flex-grow h-full w-full overflow-auto pt-4">
                      {isLoading && (
                        <div className="flex gap-2 items-center justify-center h-full">
                          <div className="animate-spin h-5 w-5 border-2 border-semantic-gray-900 rounded-full border-t-transparent" />
                          <p className="typography-body-sm text-semantic-gray-700">Rendering diagram...</p>
                        </div>
                      )}
                      {error && (
                        <p className="typography-body-sm text-red-600 text-center">
                          Unable to render diagram. Please check the syntax or try again.
                        </p>
                      )}
                      <div className="mermaid h-full w-full" ref={modalMermaidRef} id={`mermaid-modal-${id}`} />
                    </div>
                  </div>
                </Dialog.Content>
              </Dialog.Portal>
            </Dialog.Root>
          </div>
        </div>

        <div className="p-4">
          <div
            className={`flex-grow overflow-auto relative ${isZoomEnabled ? 'cursor-grab' : ''} active:${isZoomEnabled ? 'cursor-grabbing' : ''}`}
            style={{
              height: `${diagramHeight}px`,
              minHeight: '100px',
              transition: 'height 0.3s ease',
            }}
          >
            {isLoading && (
              <div className="flex gap-2 items-center justify-center h-full">
                <div className="animate-spin h-5 w-5 border-2 border-semantic-gray-900 rounded-full border-t-transparent" />
                <p className="typography-body-sm text-semantic-gray-700">Rendering diagram...</p>
              </div>
            )}
            {error && (
              <p className="typography-body-sm text-red-600 text-center">
                Unable to render diagram. Please check the syntax or try again.
              </p>
            )}
            <div className="mermaid h-full w-full" ref={mermaidRef} id={`mermaid-${id}`} style={{
              position: 'relative',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default MermaidChart;