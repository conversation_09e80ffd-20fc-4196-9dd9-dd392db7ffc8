"use client";
import React, { useRef, useEffect, useState } from "react";
import { useCodeGeneration } from "@/components/Context/CodeGenerationContext";
import {
  FaTrashAlt,
  FaWifi,
  FaExclamationCircle,
  <PERSON>a<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "react-icons/fa";
import { ArrowDownCircle, ArrowUpCircle } from "lucide-react";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import Search from "@/components/BrowsePanel/TableComponents/Search";
import { BootstrapTooltip } from "@/components/UIComponents/ToolTip/Tooltip-material-ui";

import "@/styles/tabs/codeGenerationPanel/DebugPanel.css";

const MessageTypeFilter = ({ types, selectedTypes, onToggle }) => {
  const getMessageStyle = (type) => {
    switch (type?.toLowerCase()) {
      case "agent_message":
        return "type-agent_message";
      case "terminal_output":
        return "type-terminal_output";
      case "function_call":
        return "type-function_call";
      case "browser_output":
        return "type-browser_output";
      case "progress_update":
        return "type-progress_update";
      case "cost_update":
        return "type-cost_update";
      case "status_update":
        return "type-status_update";
      case "file_watch":
        return "type-file_watch";
      default:
        return "";
    }
  };

  return (
    <div className="debug-type-filters">
      {types.map((type) => (
        <button
          key={type}
          onClick={() => onToggle(type)}
          className={`debug-type-button ${
            selectedTypes.has(type)
              ? getMessageStyle(type)
              : 'debug-type-button-inactive'
          }`}
        >
          {type}
        </button>
      ))}
    </div>
  );
};

const DebugMessage = ({ message, index, onCopy, copiedIndex }) => {
  const getMessageStyle = (type) => {
    switch (type?.toLowerCase()) {
      case "agent_message":
        return "type-agent_message";
      case "terminal_output":
        return "type-terminal_output";
      case "function_call":
        return "type-function_call";
      case "browser_output":
        return "type-browser_output";
      case "progress_update":
        return "type-progress_update";
      case "cost_update":
        return "type-cost_update";
      case "status_update":
        return "type-status_update";
      case "file_watch":
        return "type-file_watch";
      default:
        return "";
    }
  };

  return (
    <div className="debug-message group">
      <div className="debug-message-header">
        <div className="debug-message-meta">
          <span className="debug-message-time">
            {new Date(message.timestamp).toLocaleTimeString()}
          </span>
          <span className={`debug-message-type ${getMessageStyle(message.type)}`}>
            {message.type}
          </span>
        </div>
        <button
          onClick={() => onCopy(message.content, index)}
          className="debug-message-copy group-hover:opacity-100"
          title="Copy message"
        >
          {copiedIndex === index ? (
            <FaCheck className="w-4 h-4 text-green-500" />
          ) : (
            <FaCopy className="w-4 h-4 text-semantic-gray-500" />
          )}
        </button>
      </div>
      <div className="debug-message-content">{message.content}</div>
    </div>
  );
};

const DebugPanel = () => {
  const { debugMessages, clearDebugMessages, wsStatus } = useCodeGeneration();
  const messagesContainerRef = useRef(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedTypes, setSelectedTypes] = useState(new Set());
  const [isScrolledToBottom, setIsScrolledToBottom] = useState(true);
  const shouldAutoScrollRef = useRef(true);
  const [copiedIndex, setCopiedIndex] = useState(null);

  const messageTypes = [
    "agent_message",
    "terminal_output",
    "function_call",
    "browser_output",
    "progress_update",
    "cost_update",
    "status_update",
    "file_watch",
  ];

  const filteredMessages = React.useMemo(() => {
    if (!debugMessages || !Array.isArray(debugMessages)) return [];

    return debugMessages.filter((message) => {
      const matchesSearch =
        (message?.content || "").toLowerCase().includes(searchTerm.toLowerCase()) ||
        (message?.type || "").toLowerCase().includes(searchTerm.toLowerCase());
      const matchesType =
        selectedTypes.size === 0 || selectedTypes.has(message?.type);
      return matchesSearch && matchesType;
    });
  }, [debugMessages, searchTerm, selectedTypes]);

  const scrollToBottom = (force = false) => {
    if (!messagesContainerRef.current || (!shouldAutoScrollRef.current && !force)) return;

    const scrollToEnd = () => {
      if (messagesContainerRef.current) {
        messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
      }
    };

    scrollToEnd();
    requestAnimationFrame(scrollToEnd);
    setTimeout(scrollToEnd, 50);
  };

  useEffect(() => {
    scrollToBottom();
  }, []);

  useEffect(() => {
    if (shouldAutoScrollRef.current && debugMessages?.length > 0) {
      scrollToBottom();
    }
  }, [debugMessages]);

  useEffect(() => {
    const handleScroll = () => {
      if (messagesContainerRef.current) {
        const { scrollTop, scrollHeight, clientHeight } = messagesContainerRef.current;
        const isBottom = scrollTop + clientHeight >= scrollHeight - 1000;
        setIsScrolledToBottom(isBottom);
        shouldAutoScrollRef.current = isBottom;
      }
    };

    const container = messagesContainerRef.current;
    container?.addEventListener("scroll", handleScroll);
    return () => container?.removeEventListener("scroll", handleScroll);
  }, []);

  const copyToClipboard = async (content, index) => {
    try {
      await navigator.clipboard.writeText(content);
      setCopiedIndex(index);
      setTimeout(() => setCopiedIndex(null), 2000);
    } catch (err) {
      
    }
  };

  const toggleMessageType = (type) => {
    setSelectedTypes(prev => {
      const newTypes = new Set(prev);
      if (newTypes.has(type)) {
        newTypes.delete(type);
      } else {
        newTypes.add(type);
      }
      return newTypes;
    });
  };

  const getConnectionIcon = () => {
    switch (wsStatus) {
      case "connected":
        return <FaWifi className="status-icon status-icon-connected" />;
      case "connecting":
        return <FaSpinner className="status-icon status-icon-connecting" />;
      case "disconnected":
      case "error":
        return <FaExclamationCircle className="status-icon status-icon-error" />;
      default:
        return null;
    }
  };

  return (
    <div className="debug-panel">
      <div className="debug-header">
        <div className="debug-header-content">
          <div className="debug-title-section">
            <h2 className="debug-title">Debug Console</h2>
            <div className="flex items-center space-x-1">
              {getConnectionIcon()}
              <span className="status-text">{wsStatus}</span>
            </div>
          </div>
          <div className="debug-controls">
            <MessageTypeFilter
              types={messageTypes}
              selectedTypes={selectedTypes}
              onToggle={toggleMessageType}
            />
            <Search
              placeholder="Search messages..."
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
              className="debug-search"
            />
          </div>
        </div>
      </div>

      <div ref={messagesContainerRef} className="debug-messages">
        <div className="debug-messages-content">
          {!debugMessages || filteredMessages.length === 0 ? (
            <div className="debug-empty-state">
              {!debugMessages || debugMessages.length === 0 ? (
                <EmptyStateView type="DebugPanelEmptyState" />
              ) : (
                <p className="debug-no-results">No messages match your search criteria</p>
              )}
            </div>
          ) : (
            filteredMessages.map((message, index) => (
              <DebugMessage
                key={`${message.type}-${message.timestamp}-${index}`}
                message={message}
                index={index}
                onCopy={copyToClipboard}
                copiedIndex={copiedIndex}
              />
            ))
          )}
        </div>
      </div>

      <div className="debug-footer">
        <span className="debug-message-count">
          {filteredMessages?.length || 0} of {debugMessages?.length || 0} messages
        </span>

        <div className="debug-footer-controls">
          <BootstrapTooltip
            title={isScrolledToBottom ? "Scroll to top" : "Scroll to bottom"}
            placement="top"
          >
            <button
              onClick={() => {
                if (isScrolledToBottom) {
                  messagesContainerRef.current.scrollTop = 0;
                  setIsScrolledToBottom(false);
                } else {
                  scrollToBottom(true);
                  setIsScrolledToBottom(true);
                }
              }}
              className="debug-scroll-button"
            >
              {isScrolledToBottom ? (
                <ArrowUpCircle className="debug-scroll-icon" />
              ) : (
                <ArrowDownCircle className="debug-scroll-icon" />
              )}
            </button>
          </BootstrapTooltip>

          <BootstrapTooltip title="Clear console" placement="top">
            <button onClick={clearDebugMessages} className="debug-clear-button">
              <FaTrashAlt className="debug-clear-icon" />
            </button>
          </BootstrapTooltip>
        </div>
      </div>
    </div>
  );
};

export default DebugPanel;