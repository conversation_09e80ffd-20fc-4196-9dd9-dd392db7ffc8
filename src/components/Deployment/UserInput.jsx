import React, { useState, useEffect, useContext } from "react";
import { getDeploymentForm, updateDeploymentConfig } from "@/utils/api";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";

const UserInput = () => {
  const [formConfig, setFormConfig] = useState(null);
  const [formData, setFormData] = useState({});
  const [error, setError] = useState(null);
  const [isDeploying, setIsDeploying] = useState(false);
  const { showAlert } = useContext(AlertContext);

  // Get URL parameters
  const urlParams = new URLSearchParams(window.location.search);
  // const containerId = urlParams.get("node_id");
  // const projectId = window.location.pathname.split("/")[2];
 
  // const containerId = 44388
  // const projectId = 44195

  const containerId = window.location.pathname.split("/")[4];
  const projectId = window.location.pathname.split("/")[3];


  const initializeFormData = (config) => {
    const initialData = {};
    config.fields.forEach((field) => {
      initialData[field.name] = field.value;
    });
    return initialData;
  };

  useEffect(() => {
    const fetchFormConfig = async () => {
      try {
        const response = await getDeploymentForm(projectId, containerId);
        setFormConfig(response.form_config);
        setFormData(initializeFormData(response.form_config));
      } catch (err) {
        setError(err.message);
        showAlert("Failed to fetch form configuration: " + err.message);
      }
    };

    fetchFormConfig();
  }, [projectId, containerId]);

  const handleInputChange = (name, value) => {
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsDeploying(true);
    
    try {
      const response = await updateDeploymentConfig(projectId, containerId, formData);
      showAlert("Deployment configuration updated successfully");
    } catch (error) {
      showAlert("Failed to update deployment config: " + error.message);
    } finally {
      setIsDeploying(false);
    }
  };

  if (error) return <EmptyStateView type="error" message={error} />;
  if (!formConfig) return <EmptyStateView type="noUsers" />;

  const repoField = formConfig.fields.find((f) => f.name === "repo_url");
  const otherFields = formConfig.fields.filter((f) => f.name !== "repo_url");

  return (
    <div className="p-2">
      <h2 className="typography-heading-4 font-weight-bold mb-2 -mt-2.5">{formConfig.title}</h2>
      <p className="mb-3 text-semantic-gray-600">{formConfig.description}</p>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <label className="block typography-body-sm font-weight-medium">
            {repoField.label}
            {repoField.required && <span className="text-red-500">*</span>}
          </label>
          <input
            type={repoField.type}
            name={repoField.name}
            value={formData[repoField.name] || ''}
            onChange={(e) => handleInputChange(repoField.name, e.target.value)}
            placeholder={repoField.placeholder}
            className="w-full p-1 border rounded-md"
            required={repoField.required}
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          {otherFields.map((field) => (
            <div key={field.name} className="space-y-2">
              <label className="block typography-body-sm font-weight-medium">
                {field.label}
                {field.required && <span className="text-red-500">*</span>}
              </label>

              {field.type === "select" ? (
                <select
                  name={field.name}
                  value={formData[field.name] || ''}
                  onChange={(e) => handleInputChange(field.name, e.target.value)}
                  className="w-full p-1 border rounded-md"
                  required={field.required}
                >
                  {field.options.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              ) : (
                <input
                  type={field.type}
                  name={field.name}
                  value={formData[field.name] || ''}
                  onChange={(e) => handleInputChange(field.name, e.target.value)}
                  placeholder={field.placeholder}
                  className="w-full p-1 border rounded-md"
                  required={field.required}
                />
              )}
            </div>
          ))}
        </div>

        <button
          type="submit"
          disabled={isDeploying}
          className="w-full bg-primary-600 text-white py-2 px-4 rounded-md hover:bg-primary-700 disabled:bg-primary-400 disabled:cursor-not-allowed"
        >
          {isDeploying ? "Updating Configuration..." : formConfig.submit.label}
        </button>
      </form>
    </div>
  );
};

export default UserInput;