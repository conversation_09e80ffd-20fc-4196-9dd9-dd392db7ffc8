import React, { useEffect, useState } from 'react';
import { X, UserPlus, Users, UserX, UserCheck } from "lucide-react";
import AlertBox from "../AlertBox";
import MultiSelectComponent from "../UIComponents/Inputs/SelectInput/MultiSelect";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import { DynamicButton } from '../UIComponents/Buttons/DynamicButton';
import { SearchInput } from '../UIComponents/Inputs/SearchInput';

interface User {
    Username: string;
    Name: string;
    Email: string;
    added?: boolean;
}

interface UserInDiscussion {
    user_id: string;
}

interface UserSelection {
    user_id: string;
    role: string;
    responsibilities: string[];
}

interface ResponsibilityOption {
    value: string;
    label: string;
}

interface RoleOption {
    value: string;
    label: string;
}

interface AddUserProjectProps {
    filteredUsers: User[];
    setFilteredUsers: (users: User[]) => void;
    handleSubmit: (event: any, users: UserSelection[]) => Promise<void>;
    setIsOpen: (isOpen: boolean) => void;
    usersInDiscussion: UserInDiscussion[];
    isOpen: boolean;
}

const responsibilityOptions: ResponsibilityOption[] = [
    { value: "deployment", label: "Deployment" },
    { value: "testing", label: "Testing" },
    { value: "design", label: "Design" },
    { value: "documentation", label: "Documentation" },
    { value: "support", label: "Support" },
    { value: "maintenance", label: "Maintenance" },
    { value: "analysis", label: "Analysis" },
    { value: "integration", label: "Integration" },
];

const roleOptions: RoleOption[] = [
    { value: "Developer", label: "Developer" },
    { value: "Designer", label: "Designer" },
    { value: "ProductManager", label: "Product Manager" },
    { value: "Tester", label: "Tester" },
    { value: "DevOps", label: "DevOps" },
];

const UserCard: React.FC<{
    user: User;
    setAddingUser: (user: User | null) => void;
    onRemoveUser: (username: string) => void;
    usersInDiscussion: UserInDiscussion[];
}> = ({ user, setAddingUser, onRemoveUser, usersInDiscussion }) => {
    const isUserInDiscussion = usersInDiscussion.some(
        (discussionUser) => discussionUser.user_id === user.Username
    );

    const getButtonText = () => {
        return isUserInDiscussion ? "Added" : user.added ? "Remove" : "Add";
    };

    const getButtonIcon = () => {
        return isUserInDiscussion ? UserCheck : user.added ? UserX : UserPlus;
    };

    const getButtonClass = () => {
        if (isUserInDiscussion) {
            return "text-semantic-gray-400 cursor-not-allowed bg-semantic-gray-100";
        }
        return user.added
            ? "text-red-600 hover:bg-red-50"
            : "text-primary hover:bg-primary-50";
    };

    const getButtonVariant = () => {
        if (isUserInDiscussion) {
            return "linkDisable";
        }
        return user.added
            ? "linkDanger"
            : "linkPrimary";
    };

    const getInitials = () => {
        const nameParts = user.Name.split(" ");
        return nameParts.length > 0
            ? nameParts.map((part) => part.charAt(0).toUpperCase()).join("")
            : user.Username.slice(0, 2).toUpperCase();
    };

    const getInitialsColor = () => {
        // Hash the user's name to get a consistent color
        const hash = user.Name.split('').reduce((acc, char) => ((acc << 5) - acc) + char.charCodeAt(0), 0);
        const hue = hash % 360;
        return `hsl(${hue}, 80%, 60%)`;
    };

    return (
        <div className="flex items-center p-4 rounded-lg border border-semantic-gray-100 mb-2 hover:bg-semantic-gray-50 transition-all">
            <div className="relative inline-flex items-center justify-center w-10 h-10 overflow-hidden rounded-full mr-4" style={{ backgroundColor: getInitialsColor() }}>
                <span className="font-weight-medium text-white">{getInitials()}</span>
            </div>
            <div className="flex-1">
                <h3 className="font-weight-medium text-semantic-gray-900">
                    {user.Name || "Unknown User"}
                </h3>
                <p className="typography-body-sm text-semantic-gray-500">
                    {user.Email || "No email provided"}
                </p>
            </div>
            <DynamicButton
                variant={getButtonVariant()}
                icon={getButtonIcon()}
                size="default"
                onClick={() =>
                    !isUserInDiscussion && !user.added
                        ? setAddingUser(user)
                        : !isUserInDiscussion && onRemoveUser(user.Username)
                }
                text={getButtonText()}
                disabled={isUserInDiscussion}
            />
        </div>
    );
};

const AddUserProject: React.FC<AddUserProjectProps> = ({
    filteredUsers,
    setFilteredUsers,
    handleSubmit,
    setIsOpen,
    usersInDiscussion,
    isOpen,
}) => {
    const [addingUser, setAddingUser] = useState<User | null>(null);
    const [selectedOptions, setSelectedOptions] = useState<ResponsibilityOption[]>([]);
    const [selectedUsers, setSelectedUsers] = useState<UserSelection[]>([]);
    const [selectRole, setSelectRole] = useState<string>("");
    const [searchTerm, setSearchTerm] = useState<string>("");
    const [allUsers, setAllUsers] = useState<User[]>([]);
    const [users, setShowFilteredUsers] = useState<User[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [showWarning, setShowWarning] = useState<boolean>(false);

    useEffect(() => {
        setShowFilteredUsers(
            filteredUsers.map((user) => ({ ...user, added: false }))
        );
        setAllUsers(filteredUsers.map((user) => ({ ...user, added: false })));
    }, [filteredUsers]);

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const searchTerm = e.target.value;
        setSearchTerm(searchTerm);
        const filtered = allUsers.filter((user) =>
            user.Email.toLowerCase().includes(searchTerm.toLowerCase())
        );
        setShowFilteredUsers(filtered);
    };

    const onAddUser = () => {
        if (!addingUser || !selectRole) return;

        setSelectedUsers([...selectedUsers, {
            user_id: addingUser.Username,
            role: selectRole,
            responsibilities: selectedOptions.map(item => item.value),
        }]);

        setSelectRole("");
        setSearchTerm("");
        setShowFilteredUsers(allUsers.map((user) => ({...user, added: selectedUsers.some((u) => u.user_id === user.Username) || addingUser.Username === user.Username})));
        setSelectedOptions([]);
        setAddingUser(null);
    };

    const onRemoveUser = (username: string) => {
        setSelectedUsers(selectedUsers.filter(user => user.user_id !== username));
        setShowFilteredUsers(users.map(user =>
            user.Username === username ? { ...user, added: false } : user
        ));
    };

    const clearSearch = () => {
        setSearchTerm('');
        setShowFilteredUsers(allUsers);
    };

    const onSubmit = async () => {
        if (selectedUsers.length === 0) {
            setShowWarning(true);
            return;
        }

        setIsLoading(true);
        try {
            await handleSubmit(null, selectedUsers);
            setSelectedUsers([]);
            setIsOpen(false);
        } catch (error) {

        } finally {
            setIsLoading(false);
        }
    };

    // Custom modal content
    const modalContent = (
        <div className="fixed inset-0 flex items-center justify-center z-50">
            <div className="fixed inset-0 bg-black/50 backdrop-blur-sm" onClick={() => setIsOpen(false)} />
            <div className="relative bg-white rounded-lg p-6 max-w-[600px] w-full max-h-[90vh] overflow-y-auto custom-scrollbar">
                <div className="flex justify-between items-center mb-4">
                    <div className="flex justify-between items-center gap-2">
                        <Users className="h-5 w-5 text-semantic-gray-500" />
                        <h2 className="typography-heading-4 font-weight-semibold">Add User</h2>
                    </div>
                    <DynamicButton
                        variant="ghost"
                        size="sqSmall"
                        icon={X}
                        onClick={() => setIsOpen(false)}
                        tooltip="Close"
                    />
                </div>

                {!addingUser ? (
                    <>
                        <div className="relative mb-4">
                            <SearchInput
                                value={searchTerm}
                                onChange={handleSearchChange}
                                placeholder="Search users by email..."
                            />
                        </div>

                        <div className="max-h-[400px] overflow-y-auto pr-2 main-content-area">
                            {users.length > 0 ? (
                                users.map((user) => (
                                    <UserCard
                                        key={user.Username}
                                        user={user}
                                        setAddingUser={setAddingUser}
                                        onRemoveUser={onRemoveUser}
                                        usersInDiscussion={usersInDiscussion}
                                    />
                                ))
                            ) : allUsers.length === 0 ? (
                                <EmptyStateView type="noUsers" onClick={() => { }} />
                            ) : (
                                <EmptyStateView type="noSearchResults" onClick={clearSearch} />
                            )}
                        </div>

                        <div className="flex justify-end mt-6">
                            <DynamicButton
                                variant="primary"
                                icon={UserPlus}
                                text={isLoading ? "Adding Users..." : "Add Selected Users"}
                                disabled={isLoading}
                                isLoading={isLoading}
                                onClick={onSubmit}
                                tooltip={`Add users to project`}
                            />
                        </div>
                    </>
                ) : (
                    <div className="space-y-4">
                        <UserCard
                            user={addingUser}
                            setAddingUser={setAddingUser}
                            onRemoveUser={onRemoveUser}
                            usersInDiscussion={usersInDiscussion}
                        />

                        <div className="space-y-4">
                            <div>
                                <label className="block typography-body-sm font-weight-medium text-semantic-gray-700 mb-1">
                                    Role
                                </label>
                                <select
                                    value={selectRole}
                                    onChange={(e) => setSelectRole(e.target.value)}
                                    className="w-full p-2 border rounded-md"
                                >
                                    <option value="">Select a role</option>
                                    {roleOptions.map((role) => (
                                        <option key={role.value} value={role.value}>
                                            {role.label}
                                        </option>
                                    ))}
                                </select>
                            </div>

                            <div>
                                <label className="block typography-body-sm font-weight-medium text-semantic-gray-700 mb-1">
                                    Responsibilities
                                </label>
                                <MultiSelectComponent
                                    options={responsibilityOptions}
                                    selectedOptions={selectedOptions}
                                    setSelectedOptions={setSelectedOptions}
                                />
                            </div>

                            <div className="flex justify-end pt-4">
                                <DynamicButton
                                    variant="primary"
                                    icon={UserPlus}
                                    text="Add user"
                                    onClick={onAddUser}
                                    tooltip={`Add user`}
                                    disabled={selectedOptions.length==0 || !selectRole}
                                />
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );

    return (
        <>
            {isOpen && modalContent}
            {showWarning && (
                <AlertBox
                    title="Warning"
                    type="warning"
                    content="Please select at least one user before proceeding."
                    onClose={() => setShowWarning(false)}
                />
            )}
        </>
    );
};

export default AddUserProject;