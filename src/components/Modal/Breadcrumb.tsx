import { FC } from 'react';
import Link from 'next/link';
import { ChevronRight } from 'lucide-react';

interface BreadcrumbProps {
  items: {
    label: string;
    href: string;
    active?: boolean;
  }[];
}

const Breadcrumb: FC<BreadcrumbProps> = ({ items }) => {
  return (
    <nav className="flex items-center space-x-2 mb-4 mt-2">
      {items.map((item, index) => (
        <div key={item.href} className="flex items-center">
          {index > 0 && <ChevronRight className="h-4 w-4 text-semantic-gray-400 mx-2" />}
          <Link
            href={item.href}
            className={`breadcrumb-title hover:text-primary ${
              item.active 
                ? 'text-semantic-gray-900 font-weight-medium' 
                : 'text-semantic-gray-500 '
            }`}
          >
            {item.label}
          </Link>
        </div>
      ))}
    </nav>
  );
};

export default Breadcrumb;