"use client";
import React, { useState, useEffect, useRef, useContext } from "react";
import { useParams } from "next/navigation";
import { configureNodeWithAgent, deleteTask, getAvailableOptions } from "@/utils/api";
import { ExecutionContext } from "../Context/ExecutionContext";
import { AlertContext } from "../NotificationAlertService/AlertList";
import { Box, ClipboardList, ListChecks, Pen} from "lucide-react";
import EitherOrModal from "./EitherOrModal";


const ConfigureModal = (props) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [allSelected, setAllSelected] = useState(false);
  const [levels, setLevels] = useState("");
  const modalRef = useRef(null); // Create a ref for the modal
  const { showAlert } = useContext(AlertContext);
  const { setCurrentTaskId, setIsNodeType,currentTaskId } = useContext(ExecutionContext);

  // Approval Task Modal
  const [approvalOpen,setApprovalOpen] = useState(false);
  

  const params = useParams();
  let projectId = params.projectId;

  useEffect(() => {
    setIsNodeType(props.isNodeType);
  }, [props.isNodeType, setIsNodeType]);

  useEffect(() => {
    // Function to handle click outside of modal
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        props.closeModal();
      }
    };

    // Bind the event listener
    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      // Unbind the event listener on cleanup
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [props.closeModal]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    if (name === "levels") {
      setLevels(value);
    }
  };


  const toggleAllOptions = () => {
    const newState = !allSelected;
    setAllSelected(newState);
    
    const updateAllOptions = (obj) => {
      Object.keys(obj).forEach((key) => {
        if (typeof obj[key] === 'object') {
          // Don't modify the root configure property
          if (key !== 'configure') {
            obj[key].configure = newState;
            updateAllOptions(obj[key]);
          }
        }
      });
    };
  
    const updatedConfig = { ...config };
    updateAllOptions(updatedConfig);
    setConfig(updatedConfig);
  };

  const handleSubmit = async (e) => {
     e.preventDefault();

    const hasSelectedOptions = Object.values(config).some((item) => {
      if (typeof item === 'object') {
        return item.configure || Object.values(item).some(subItem => subItem.configure);
      }
      return false;
    });

    if (!hasSelectedOptions) {
      showAlert("Please select at least one option", "danger");
      return;
    }
    if(config?.requirements?.configure === false && config?.work_item?.configure === false && config?.architecture?.configure === true){
      showAlert("Please complete the configuration of Work Items and Requirements before configuring the Architecture. Skipping these steps may result in errors or an incomplete project setup","warning")  
      return    
    }
    
    
    
    setIsSubmitting(true);
    
    if (config) {
      config.configure = true;
    }
    
    let configuration = config
    configuration.configure = true;

    try {
      const response = await configureNodeWithAgent({
        node_id: props.requirementId || props.id,
        node_type: props.isNodeType,
        user_level: 1,
        project_id: projectId,
        configurations: configuration
      });

      // Check if the response contains an error about an existing task
      if (response.error === "You already have a task in progress. Please cancel the current task to start a new one.") {
        showAlert(response.error, "danger");
        localStorage.setItem("current_task_id", response.task_id);
        setCurrentTaskId(response.task_id);
        setApprovalOpen(true);
        
      } else {
        // Handle success case
        setCurrentTaskId(response.task_id);
        props.closeModal();
        props.onSubmitSuccess();
      }
    } catch (error) {
      showAlert("Error configuring node", "danger");
    } finally {
      setIsSubmitting(false);
    }
  };

  const [config, setConfig] = useState(null);

  useEffect(() => {
    const loadConfig = async () => {
      props.setLoadingAutoConfigure(true);
      const data = await getAvailableOptions(props.type ? props.type : 'requirement');
      if (data && typeof data === 'object') {
        data.configure = true;
      }
      setConfig(data);
      props.setLoadingAutoConfigure(false);
    }

    if (!config) {
      loadConfig();
    }
  }, [config, props.type]);
  
  const handleToggle = (path) => {
    const keys = path.split('.');
  
    // Function to enable parents
    const enableParents = (obj, keys) => {
      if (keys.length > 1) {
        const parentKeys = keys.slice(0, keys.length - 1);
        const parentPath = parentKeys.join('.');
        const currentKeys = parentPath.split('.');
  
        const updateParent = (obj, currentKeys) => {
          if (currentKeys.length === 1) {
            obj[currentKeys[0]].configure = true;
          } else {
            obj[currentKeys[0]].configure = true;
            updateParent(obj[currentKeys[0]], currentKeys.slice(1));
          }
        };
  
        updateParent(obj, currentKeys);
      }
    };
  
    // Function to disable all child elements
    const disableChildren = (obj) => {
      Object.keys(obj).forEach((key) => {
        if (typeof obj[key] === 'object') {
          obj[key].configure = false;
          disableChildren(obj[key]);
        }
      });
    };
  
    // Function to toggle current item and handle parent-child relationships
    const toggleState = (obj, keys) => {
      if (keys.length === 1) {
        // Don't allow toggling the root configure property
        if (keys[0] === 'configure') return;
        
        obj[keys[0]].configure = !obj[keys[0]].configure;
        
        if (obj[keys[0]].configure) {
          // Only enable parents when turning on
          enableParents(obj, keys);
        } else {
          // Disable all children when turning off
          disableChildren(obj[keys[0]]);
        }
      } else {
        enableParents(obj, keys);
        toggleState(obj[keys[0]], keys.slice(1));
      }
    };

    const updatedConfig = { ...config };
    toggleState(updatedConfig, keys);
    setConfig(updatedConfig);
  };

  
  
  
  
  

  const getHeadIcon = (name, configure, currentPath) => {
    const iconProps = {
      className: `w-5 h-5 ${configure ? 'text-primary' : 'text-black'} cursor-pointer`,
      fill: configure ? 'currentColor' : 'none',
      onClick: () => handleToggle(currentPath),
    };

    switch (name.toLowerCase()) {
      case "architecture":
        return <Box {...iconProps} />;
      case "requirements":
        return <Pen {...iconProps} />;
      case "task":
        return <ClipboardList {...iconProps} />
      default:
        return <ListChecks {...iconProps} />;
    }
  };


  const renderOptions = (obj, path = '', head = true) => {
    return (
      <div className="">
        {Object.keys(obj).map((key) => {
          if (typeof obj[key] === 'object') {
            const currentPath = path ? `${path}.${key}` : key;
            return (
              <div key={currentPath} className={`mr-6 mb-2 ${!head && 'ml-6'}`}>
                <div className="flex items-center">
                  {/* Use radio button or checkbox based on your design */}
                  {/* {head && getHeadIcon(`${key.charAt(0).toUpperCase() + key.slice(1)}`.replace('_', ' '),obj[key].configure,currentPath)} */}
                  <input 
                    id={`${currentPath}-checkbox`} 
                    type="checkbox" 
                    value="" 
                    className={`w-4 h-4 text-primary bg-semantic-gray-100 border-semantic-gray-300 rounded focus:ring-primary dark:focus:ring-primary-600 focus:ring-2  cursor-pointer `}
                    checked={obj[key].configure}
                    onChange={() => handleToggle(currentPath)}
                  />
                  <label
                    htmlFor={`${currentPath}-checkbox`}
                    className={`ml-2 typography-body-lg   ${head ? "font-weight-bold text-black" : "font-weight-medium text-semantic-gray-500"}`}>
                    {`${key.charAt(0).toUpperCase() + key.slice(1)}`.replace('_', ' ')}
                  </label>
                </div>
                {/* Render sub-options */}
                <div className={` ${head ? "mt-4":"ml-6"}`}>
                  {renderOptions(obj[key], currentPath,false)}
                </div>
              </div>
            );
          }
          return null;
        })}
      </div>
    );
  };
  const [processing,setProcessing] = useState(false);
  
  if (approvalOpen){
    return <EitherOrModal isOpen={approvalOpen} isProcessing={processing} onClose={()=>{setApprovalOpen(false)}}    type={'task-approval'} onAction={(e)=>{
      (async()=>{
        setProcessing(true);
        await deleteTask(currentTaskId,true);
        await handleSubmit(e);
        setProcessing(false);
        props.closeModal();
      })();
    }} buttons={[
      { name: 'Cancel', className: 'bg-semantic-gray-200 text-semantic-gray-800 hover:bg-semantic-gray-300' },
      { name: 'Overwrite Task', className: 'bg-red-600 text-white hover:bg-red-700' }
    ]}/>
  }

  return (
    <>
    
    {config ? <div className="fixed inset-0 z-50 overflow-y-auto flex items-center justify-center bg-semantic-gray-600/50 p-4" onClick={()=>props.closeModal} >
      <div ref={modalRef} className="bg-custom-bg-primary max-w-md w-full rounded-lg p-6 sm:p-8" onClick={(e)=>e.stopPropagation()}>
        {/* Modal Header */}
        <div className="mb-6 text-center flex justify-between border-b-2 border-custom-border pb-2">
          <div></div>
          <h2 className="typography-body-lg sm:typography-heading-4 font-weight-bold text-custom-text-primary">Auto Configure</h2>
          <button
            className="project-panel-title bg-semantic-gray-200 px-2 rounded-2xl "
            onClick={props.closeModal}
          >
            <svg
              width="12"
              height="13"
              viewBox="0 0 12 13"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M7.20988 6.5L11.7388 1.97109C11.8205 1.89216 11.8857 1.79775 11.9305 1.69336C11.9754 1.58896 11.999 1.47669 12 1.36308C12.001 1.24946 11.9793 1.13679 11.9363 1.03164C11.8933 0.926484 11.8297 0.83095 11.7494 0.750611C11.669 0.670273 11.5735 0.606739 11.4684 0.563716C11.3632 0.520694 11.2505 0.499045 11.1369 0.500032C11.0233 0.50102 10.911 0.524624 10.8066 0.569467C10.7023 0.61431 10.6078 0.679495 10.5289 0.761217L6 5.29012L1.47109 0.761217C1.30972 0.605355 1.09358 0.519111 0.869234 0.521061C0.644888 0.52301 0.430283 0.612997 0.27164 0.77164C0.112997 0.930283 0.0230102 1.14489 0.0210607 1.36923C0.0191112 1.59358 0.105355 1.80972 0.261217 1.97109L4.79012 6.5L0.261217 11.0289C0.179495 11.1078 0.11431 11.2023 0.0694668 11.3066C0.0246235 11.411 0.00101956 11.5233 3.23065e-05 11.6369C-0.000954946 11.7505 0.020694 11.8632 0.0637164 11.9684C0.106739 12.0735 0.170273 12.169 0.250611 12.2494C0.33095 12.3297 0.426484 12.3933 0.531639 12.4363C0.636794 12.4793 0.749465 12.501 0.863076 12.5C0.976687 12.499 1.08896 12.4754 1.19336 12.4305C1.29775 12.3857 1.39216 12.3205 1.47109 12.2388L6 7.70988L10.5289 12.2388C10.6903 12.3946 10.9064 12.4809 11.1308 12.4789C11.3551 12.477 11.5697 12.387 11.7284 12.2284C11.887 12.0697 11.977 11.8551 11.9789 11.6308C11.9809 11.4064 11.8946 11.1903 11.7388 11.0289L7.20988 6.5Z"
                fill="hsl(var(--semantic-gray-900))"
              />
            </svg>
          </button>
        </div>
        <div className="flex justify-end items-center mb-4">
    
          <button
            type="button"
            onClick={toggleAllOptions}
            className="text-primary hover:text-primary-800 font-weight-medium"
          >
            {allSelected ? 'Deselect All' : 'Select All'}
          </button>
      </div>

        

          {/* Form */}
          <form onSubmit={handleSubmit}>
            {/* Levels Dropdown */}
            {/* <div className="mb-4">
            <label htmlFor="levels" className="block typography-body-sm font-weight-medium text-semantic-gray-700">
              Levels
            </label>
            <select
              id="levels"
              name="levels"
              value={levels}
              onChange={handleChange}
              className="border rounded w-full p-2 typography-body-sm sm:typography-body"
            >
              <option value="">Select Level</option>
              <option value="1">Level 1</option>
              <option value="2">Level 2</option>
              <option value="3">Level 3</option>
            </select>
          </div> */}
            <div className="mb-4 px-2 overflow-y-auto max-h-[65vh]">
              {/* <h3 className='font-weight-bold typography-body-lg mb-5' >Configuration Options</h3> */}
              {renderOptions(config)}
            </div>

            <div className="flex flex-col sm:flex-row justify-between gap-2 sm:gap-4 ">
              <div></div>
              <button
                type="submit"
                className="bg-primary text-white px-4 py-2 rounded-md"
                disabled={isSubmitting}
              >
                {isSubmitting ? "Submitting..." : "Submit"}
              </button>
              {/* <button
              type="button"
              onClick={props.closeModal}
              className="bg-red-500 text-white px-4 py-2 rounded-md"
            >
              Cancel
            </button> */}
            </div>
          </form>
        </div>
      </div> : <></>}
    </>
  );
};

export default ConfigureModal;
