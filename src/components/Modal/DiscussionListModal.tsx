import React, { useState, useEffect, useContext } from "react";
import { formatUTCToLocal } from "@/utils/datetime";
import { useRouter } from "next/navigation";
import { decrypt, getCookie } from "@/utils/auth";
import { CircularLoader } from "@/components/Loaders/Loading";
import { TopBarContext } from "@/components/Context/TopBarContext";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import { SearchInput } from '../UIComponents/Inputs/SearchInput';
import { buildProjectUrl } from '@/utils/navigationHelpers';

interface Tab {
  id: string;
  title: string;
  href: string;
}

interface TopBarContextType {
  tabs: Tab[];
  addTab: (title: string, href: string) => void;
  setActiveTab: (id: string) => void;
}

interface Discussion {
  discussion_id: string;
  project_id: string;
  project_title: string;
  task_title: string;
  description: string;
  date: string;
  index: number;
  total: number;
}

interface DiscussionCardProps extends Discussion {
  onProjectClick: (projectId: string, projectTitle: string) => void;
  onDiscussionClick: (discussionId: string, projectId: string) => void;
}

interface DiscussionListModalProps {
  discussions?: Discussion[];
  showSearch?: boolean;
  searchQuery?: string;
  clearQueryfn?:() => void
  searchQueryfn?: (value: string) => void;
}

interface User {
  email: string;
  [key: string]: any;
}

const DiscussionCard: React.FC<DiscussionCardProps> = ({
  discussion_id,
  project_id,
  project_title,
  task_title,
  description,
  date,
  onProjectClick,
  onDiscussionClick,
  index,
  total,
}) => {
  const cardClassName = `
    bg-white 
    border 
    border-semantic-gray-200 
    p-4 
    transition-all 
    duration-200 
    hover:bg-semantic-gray-50 
    ${index === 0 ? "rounded-t-lg border-t" : ""} 
    ${index === total - 1 ? "rounded-b-lg" : "border-b"} 
    ${index !== 0 && index !== total - 1 ? "border-b" : ""}
  `;

  return (
    <>
      <div className={cardClassName}>
        <div className="flex justify-between text-custom-base text-custom-text-tertiary font-weight-medium">
          <span>
            @ Mention in{" "}
            <span
              className="text-custom-text-primary"
              onClick={(e) => {
                e.stopPropagation();
                onProjectClick(project_id, project_title);
              }}
            >
              #{project_title}
            </span>
          </span>
          <div className="text-custom-text-tertiary typography-caption">{formatUTCToLocal(date)}</div>
        </div>
        <div className="flex items-center mt-custom-sm">
          <div
            className="flex flex-col cursor-pointer"
            onClick={() => onDiscussionClick(discussion_id, project_id)}
          >
            <div className="font-weight-semibold text-custom-md text-custom-text-primary">{task_title}</div>
            <div className="text-custom-base text-custom-text-tertiary">{description}</div>
          </div>
        </div>
      </div>
    </>
  );
};

const DiscussionListModal: React.FC<DiscussionListModalProps> = ({
  discussions = [],
  showSearch = false,
  searchQuery = "",
  clearQueryfn,
  searchQueryfn,
}) => {
  const [user, setUser] = useState<User>({} as User);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const router = useRouter();
  const { tabs, addTab, setActiveTab } = useContext(TopBarContext) as TopBarContextType;

  const setCurrentUser = async (): Promise<void> => {
    const token = await getCookie("idToken");
    const user = await decrypt(token);
    setUser(user);
  };

  const navigation = (projectId: string, projectTitle: string): void => {
    const discussionUrl = buildProjectUrl(projectId, 'overview');
    const existingTab = tabs.find((tab: any) => tab.href === discussionUrl);
    if (existingTab) {
      setActiveTab(existingTab.id);
      router.push(existingTab.href);
    } else {
      addTab(projectTitle, discussionUrl);
      router.push(discussionUrl);
    }
  };

  const navigateToDiscussion = (discussionId: string, projectId: string): void => {
    const currentParams = new URLSearchParams(window.location.search);
    currentParams.set("discussion_id", discussionId);
    currentParams.set("discussion", "existing");
    router.push(`${window.location.pathname}?${currentParams.toString()}`);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (searchQueryfn) {
      searchQueryfn(e.target.value);
    }
  };

  useEffect(() => {
    setCurrentUser();
  }, []);



  const sortedData = [...discussions].sort(
    (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
  );

  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center h-96">
          <CircularLoader />
        </div>
      );
    }

    if (discussions.length === 0) {
      return (
        <div className="flex items-center justify-center h-96">
          <EmptyStateView
            type={searchQuery ? "noSearchResult" : "discussions"}
            onClick={clearQueryfn}
          />
        </div>
      );
    }

    return (
      <div className="divide-y divide-semantic-gray-200">
        {sortedData.map((discussion, index) => (
          <DiscussionCard
            key={discussion.discussion_id}
            {...discussion}
            onProjectClick={navigation}
            onDiscussionClick={navigateToDiscussion}
            index={index}
            total={discussions.length}
          />
        ))}
      </div>
    );
  };

  return (
    <div className="flex flex-col h-full">
      <SearchInput
        value={searchQuery}
        onChange={handleSearchChange}
        placeholder="Search discussions..."
        withContainer={true}
      />
      <div className="flex-1 overflow-auto custom-scrollbar p-4">
        {renderContent()}
      </div>
    </div>
  );
};

export default DiscussionListModal;