"use client";
import React, { useState, useRef, useEffect, useContext } from "react";
import {
  Search,
  Eye,
  Download,
  X,
  Figma,
  Code,
  FileText,
  Plus,
  RefreshCw,
  Trash2,
} from "lucide-react";
import {
  extractTextFromFile,
  getProjectFiles,
  deleteProjectFile,
} from "@/utils/api";
import { usePathname } from "next/navigation";
import { AlertContext } from "../NotificationAlertService/AlertList";
import Cookies from "js-cookie";
import EmptyStateView from "./EmptyStateModal";
import Pagination from "../UIComponents/Paginations/Pagination";
import RepositoryList from "./RepoProjectAsset";
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import { useProjectAsset } from "../Context/ProjectAssetContext";
import { getkginfo } from "@/utils/gitAPI";
import DeleteProjectModal from "../Modal/DeleteProjectModal";
import { DriverContext } from "../Context/DriverContext";
import { FileViewerModal } from "./FileViewerModal";

const ProjectAssets = ({ onClose, handleCloseAsset }) => {
  const [filteredDocuments, setFilteredDocuments] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [hasAssets, setHasAssets] = useState(false);
  const [documentList, setDocumentList] = useState([]);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState(null);
  const [processingDocuments, setProcessingDocuments] = useState(new Set());
  const fileInputRef = useRef(null);
  const pathname = usePathname();
  const projectId = pathname.split("/")[3];
  const { showAlert } = useContext(AlertContext);
  const author = Cookies.get("username");
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(5);
  const [searchQuery, setSearchQuery] = useState("");
  const [isCodeOpen, setIsCodeOpen] = useState(false);
  const [isRepoModalOpen, setIsRepoModalOpen] = useState(false);
  const [codeLength, setCodeLength] = useState(0);
  const [clickedOption, setClickedOption] = useState("");
  const modalRef = useRef(null);
  const { setActiveTab, activeTab } = useProjectAsset();
  const [refreshClicked, setRefreshClicked] = useState(false);
  const [tabCounts, setTabCounts] = useState({
    code: 0,
    documents: 0,
    design: 0,
  });
  const [viewerModal, setViewerModal] = useState({
    isOpen: false,
    file: null,
    viewUrl: ''
  });
  const { isDriverActive, prepareTempDriver } = useContext(DriverContext);

  useEffect(() => {
    if (isDriverActive) {
      prepareTempDriver('projectAsset');
    }
  }, []);

  const fetchInitialData = async () => {
    try {
      setIsLoading(true);
      // Fetch documents
      const documentsResult = await getProjectFiles(projectId);
      if (documentsResult && documentsResult.files) {
        // Update how we format documents from the API response
        const formattedDocuments = documentsResult.files.map((file) => ({
          id: file.filename,
          name: file.filename,
          file_uuid :file.file_uuid,
          type: "PDF",
          size: formatBytes(file.size),
          author: file.uploaded_by || "Unknown",
          tags: ["document"],
          status: file.status,
          // Start with 1% progress for processing documents until we get the real percentage
          progress: file.status === "processing" ? file.percentage || 0 : 100,
          progress: file.status === "processing" ? file.percentage || 0 : 100,
          viewUrl: file.view_url,
          downloadUrl: file.download_url,
          lastModified: new Date(file.last_modified).toLocaleDateString(),
          // Add the new fields
          elapsedTime: file.elapsed_time || 0,
          remainingTime: file.remaining_time || "",
          percentage:
            file.percentage || (file.status === "processing" ? 0 : 100),
        }));
        setTabCounts((prev) => ({
          ...prev,
          documents: formattedDocuments.length,
        }));
      }

      // Fetch repositories
      const reposResult = await getkginfo(projectId, true);
      if (reposResult.details && reposResult.details.length) {
        setTabCounts((prev) => ({
          ...prev,
          code: reposResult.details.length,
        }));
      }

      // Design count remains 0 as it's coming soon
      setHasAssets(true);
    } catch (error) {
      
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchInitialData();
  }, [projectId]);

  const tabs = [
    { id: "code", label: "Code", count: tabCounts.code },
    { id: "documents", label: "Documents", count: tabCounts.documents },
    { id: "design", label: "Design", count: tabCounts.design },
  ];

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (isDeleteModalOpen || viewerModal?.isOpen) {
        return; // Don't close if delete modal or file viewer modal is open
      }
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        onClose();
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [onClose, isDeleteModalOpen, viewerModal?.isOpen]);

  // Update how we format documents from the API response
  useEffect(() => {
    if (
      clickedOption === "Upload Documents" ||
      activeTab === "documents" ||
      refreshClicked
    ) {
      if (refreshClicked) {
        setRefreshClicked(false);
      }
      const fetchProjectFiles = async () => {
        setIsLoading(true);
        try {
          const result = await getProjectFiles(projectId);
          if (result && result.files) {
            const formattedDocuments = result.files.map((file) => ({
              id: file.filename,
              name: file.filename,
              file_uuid :file.file_uuid,
              type: "PDF",
              size: formatBytes(file.size),
              author: file.uploaded_by || "Unknown",
              tags: ["document"],
              status: file.status,
              progress: file.percentage,
              viewUrl: file.view_url,
              downloadUrl: file.download_url,
              lastModified: new Date(file.last_modified).toLocaleDateString(),
              // Add the new fields
              elapsedTime: file.elapsed_time || 0,
              remainingTime: file.remaining_time || "",
              percentage: file.percentage || 0,
            }));
            setDocumentList(formattedDocuments);
            setTabCounts((prev) => ({
              ...prev,
              documents: formattedDocuments.length,
            }));
            setHasAssets(true);
          }
        } catch (error) {
          
          showAlert("Failed to fetch project files", "error");
        } finally {
          setIsLoading(false);
        }
      };

      fetchProjectFiles();
    }
  }, [projectId, clickedOption, activeTab, refreshClicked]);

  useEffect(() => {
    const style = document.createElement("style");
    style.textContent = `
      @keyframes progress-pulse {
        0% { opacity: 0.6; }
        50% { opacity: 1; }
        100% { opacity: 0.6; }
      }
      
      @keyframes blink-animation {
        0% { opacity: 1; }
        50% { opacity: 0.3; }
        100% { opacity: 1; }
      }
      
      .blink-animation {
        animation: blink-animation 1.5s ease-in-out infinite;
      }
    `;
    document.head.appendChild(style);
    return () => document.head.removeChild(style);
  }, []);

  useEffect(() => {
    // Check if there are any documents in the "processing" state
    const hasProcessingDocs = documentList.some(doc => doc.status === "processing");
  
    if (hasProcessingDocs) {
      const refreshInterval = setInterval(async () => {
        try {
          const result = await getProjectFiles(projectId);
          if (result && result.files) {
            const formattedDocuments = result.files.map((file) => {
              // Find the previous document state to detect status changes
              const prevDoc = documentList.find(doc => doc.id === file.filename);
              
              // Extract progress information from the MongoDB structure
              let progressPercentage = 0;
              let elapsedTime = 0;
              let remainingTime = "";
              
              // Check if progress is a nested object (as in your MongoDB structure)
              if (file.progress && typeof file.progress === 'object') {
                progressPercentage = file.progress.percentage || 0;
                elapsedTime = file.progress.elapsed_time || 0;
                
                // Calculate remaining time if we have the necessary data
                if (file.progress.timeout_value && progressPercentage > 0 && progressPercentage < 100) {
                  const estimatedTotal = elapsedTime / progressPercentage * 100;
                  const remaining = estimatedTotal - elapsedTime;
                  remainingTime = `${Math.round(remaining)}s`;
                }
              } else if (file.percentage) {
                // Fall back to direct percentage if available
                progressPercentage = file.percentage;
              }
              
              // Check for status changes to show notifications
              if (prevDoc && prevDoc.status === "processing") {
                if (file.status === "completed") {
                  showAlert("Document processed successfully", "success");
                } else if (file.status === "failed") {
                  showAlert("Document processing failed", "error");
                }
              }
              
              return {
                id: file.filename,
                name: file.filename,
                file_uuid: file.file_uuid,
                type: "PDF",
                size: formatBytes(file.size),
                author: file.uploaded_by || "Unknown",
                tags: ["document"],
                status: file.status,
                progress: progressPercentage,  // Use this for the progress bar
                percentage: progressPercentage, // Keep for compatibility
                elapsedTime: elapsedTime,
                remainingTime: remainingTime,
                viewUrl: file.view_url,
                downloadUrl: file.download_url,
                lastModified: new Date(file.last_modified).toLocaleDateString(),
              };
            });
            
            setDocumentList(formattedDocuments);
            setTabCounts((prev) => ({
              ...prev,
              documents: formattedDocuments.length,
            }));
          }
        } catch (error) {
          
        }
      }, 3000); // Refresh every 3 seconds
      
      // Clean up the interval when component unmounts
      return () => clearInterval(refreshInterval);
    }
  }, [documentList, projectId]);

  const handleAddDocument = () => {
    // Trigger click on the hidden file input
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };


  const handleFileChange = async (event) => {
    const files = event.target.files;
      
    if (!files || files.length === 0) return;
    
    // Show notification if uploading multiple files
    if (files.length > 1) {
      showAlert(`Uploading ${files.length} documents...`, "info");
    }
    
    // Process each file
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      
      if (file) {
        const newDocument = {
          id: Date.now() + i, // Ensure unique ID for each document
          name: file.name,
          type: file.name.split(".").pop().toUpperCase() || "PDF",
          size: formatBytes(file.size),
          author: author || "Unknown",
          tags: ["document"],
          status: "processing",
          progress: 0,
          percentage: 0,
        };
        
        setProcessingDocuments((prev) => new Set(prev).add(newDocument.id));
        setDocumentList((prev) => [...prev, newDocument]);
  
        try {
          const result = await extractTextFromFile(projectId, file);
          
          if (result) {
            setDocumentList((prevList) =>
              prevList.map((doc) =>
                doc.id === newDocument.id
                  ? {
                      ...doc,
                      status: "processing", // Keep as processing until backend updates
                      file_uuid: result.files?.[0]?.file_uuid,
                    }
                  : doc
              )
            );
          }
        } catch (error) {
          console.error("Upload error:", error);
          setDocumentList((prevList) =>
            prevList.map((doc) =>
              doc.id === newDocument.id
                ? { ...doc, status: "error", progress: 0, percentage: 0 }
                : doc
            )
          );
          showAlert(`Failed to upload ${file.name}: ${error.message || "Unknown error"}`, "error");
        } finally {
          setProcessingDocuments((prev) => {
            const newSet = new Set(prev);
            newSet.delete(newDocument.id);
            return newSet;
          });
        }
      }
    }
    
    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };
  const formatBytes = (bytes, decimals = 2) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + " " + sizes[i];
  };

  useEffect(() => {
    const style = document.createElement("style");
    style.textContent = `
      @keyframes progress-pulse {
        0% { opacity: 0.6; }
        50% { opacity: 1; }
        100% { opacity: 0.6; }
      }
    `;
    document.head.appendChild(style);
    return () => document.head.removeChild(style);
  }, []);

  const handleSearch = (query) => {
    setSearchQuery(query);

    const lowercaseQuery = query.toLowerCase();
    const filtered = documentList.filter(
      (doc) =>
        doc.name.toLowerCase().includes(lowercaseQuery) ||
        doc.type.toLowerCase().includes(lowercaseQuery) ||
        doc.size.toLowerCase().includes(lowercaseQuery) ||
        doc.author.toLowerCase().includes(lowercaseQuery) ||
        doc.tags.some((tag) => tag.toLowerCase().includes(lowercaseQuery)) ||
        (doc.status && doc.status.toLowerCase().includes(lowercaseQuery))
    );

    setFilteredDocuments(filtered);
  };

  const getCurrentPageItems = () => {
    const items = searchQuery ? filteredDocuments : documentList;
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return items.slice(startIndex, endIndex);
  };

  const DocumentLoader = () => {
    return (
      <div className="border rounded-lg p-4 animate-pulse">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {/* Document icon skeleton */}
            <div className="w-6 h-6 bg-semantic-gray-200 rounded"></div>

            <div>
              {/* Filename skeleton */}
              <div className="h-5 w-48 bg-semantic-gray-200 rounded mb-2"></div>

              {/* Info line skeleton */}
              <div className="flex items-center gap-2">
                <div className="h-4 w-12 bg-semantic-gray-200 rounded"></div>
                <div className="h-4 w-4 bg-semantic-gray-200 rounded"></div>
                <div className="h-4 w-16 bg-semantic-gray-200 rounded"></div>
                <div className="h-4 w-4 bg-semantic-gray-200 rounded"></div>
                <div className="h-4 w-24 bg-semantic-gray-200 rounded"></div>
                <div className="h-4 w-16 bg-semantic-gray-200 rounded"></div>
              </div>
            </div>
          </div>

          {/* Action buttons skeleton */}
          <div className="flex items-center gap-2">
            <div className="w-5 h-5 bg-semantic-gray-200 rounded"></div>
            <div className="w-5 h-5 bg-semantic-gray-200 rounded"></div>
          </div>
        </div>

        {/* Progress bar skeleton */}
        <div className="mt-3">
          <div className="w-full bg-semantic-gray-200 rounded-full h-2"></div>
        </div>
      </div>
    );
  };

  const handleViewDocument = (viewUrl, filename, fileData) => {
    const fileExtension = filename.split(".").pop().toLowerCase();
    // Open PDF in new tab with PDF.js viewer
    if (fileExtension === "pdf") {
      const googleDocsViewerUrl = `https://mozilla.github.io/pdf.js/web/viewer.html?file=${encodeURIComponent(viewUrl)}`;
      window.open(googleDocsViewerUrl, "_blank", "noopener,noreferrer");
    } else {
      // Open other files in modal
      setViewerModal({
        isOpen: true,
        file: { ...fileData, name: filename },
        viewUrl: viewUrl
      });
    }
  };
  const handleDownload = () => {
    showAlert("Document Downloaded Successfully", "success");
  };

  const handleDeleteButtonClick = (file_uuid,filename) => {
    setDocumentToDelete(file_uuid);
    setIsDeleteModalOpen(true);
  };

  const handleDeleteDocument = async () => {
    if (!documentToDelete) return;

    setIsDeleting(true);
    try {
      const response = await deleteProjectFile(projectId, documentToDelete);
      if (response.status === 200) {
        showAlert("Document deleted successfully.", "success");
        // Only trigger the refresh, which will handle updating everything
        setRefreshClicked(true);
      } else {
        showAlert(
          response.message || "Failed to delete the document.",
          "danger"
        );
      }
    } catch (error) {
      
      showAlert("Failed to delete the document.", "error");
    } finally {
      setIsDeleting(false);
      setIsDeleteModalOpen(false);
      setDocumentToDelete(null);
    }
  };
  const fileTypes = {
    'pdf': 'PDF',
    'png': 'PNG',
    'jpg': 'JPEG',
    'txt': 'Text File',
    'doc': 'Word Document'
  };
  const closeViewer = () => {
    setViewerModal({
      isOpen: false,
      file: null,
      viewUrl: ''
    });
  };

  return (
    <div>
      <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
        <div
          ref={modalRef}
          className="w-full min-w-[300px] max-w-[60vw] h-[88vh] bg-white rounded-lg shadow relative z-50 flex flex-col overflow-hidden"
          id="projectAssetModal"
        >
          {/* Header */}
          <div className="flex items-center justify-between px-4 py-2 border-b">
            <h2 className="typography-body-lg font-weight-medium">Project Assets</h2>
            <div className="flex gap-2">
              {/* <button className="p-1 hover:bg-semantic-gray-100 rounded">
                <Image
                  src={kebabicon}
                  alt="kebab Icon"
                  width={20}
                  height={20}
                />
              </button>
             
              <button className="p-1 hover:bg-semantic-gray-100 rounded">
                <Maximize2 className="w-5 h-5 text-semantic-gray-500" />
              </button> */}
              {/* X (close) icon */}
              <button
                className="p-1 hover:bg-semantic-gray-100 rounded"
                title="click to close"
                onClick={onClose}
              >
                <X className="w-5 h-5 text-semantic-gray-500" />
              </button>
            </div>
          </div>

          
          
          <>
            <div className="flex gap-6 mt-1 px-4 py-1">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => {
                    setIsRepoModalOpen(false);
                    setActiveTab(tab.id);
                    // Reset isCodeOpen when switching tabs
                    if (tab.id !== "code") {
                      setIsCodeOpen(false);
                    }
                  }}
                  className={`flex items-center gap-2 pb-2 ${
                    activeTab === tab.id
                      ? "border-b-2 border-primary text-primary"
                      : "text-semantic-gray-600"
                  }`}
                >
                  {tab.id === "code" && <Code className="w-5 h-5" />}
                  {tab.id === "documents" && <FileText className="w-5 h-5" />}
                  {tab.id === "design" && <Figma className="w-5 h-5" />}
                  {tab.label} ({tab.count})
                </button>
              ))}
            </div>
            {activeTab === "documents" && (
              <div className="flex flex-col h-[calc(100%-80px)]">
                {/* Search and Add Document section */}
                <div className="px-4 py-4 flex items-center justify-between gap-4">
                  <div className="relative flex-1">
                    <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-semantic-gray-400 w-5 h-5" />
                    <input
                      type="text"
                      placeholder="Search documents."
                      className="w-full pl-12 pr-8 py-1 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      value={searchQuery}
                      onChange={(e) => handleSearch(e.target.value)}
                    />
                  </div>

                  <DynamicButton
                    variant="primary"
                    icon={RefreshCw}
                    onClick={() => setRefreshClicked(true)}
                    tooltip="Refresh"
                  />

                  <DynamicButton
                    variant="primary"
                    icon={Plus}
                    text="Add Documents"
                    onClick={handleAddDocument}
                    tooltip="Add Documents"
                  />
                  {/* <button
                    className="px-4 py-2 bg-primary text-white rounded-lg flex items-center gap-2 hover:bg-primary-600"
                    onClick={handleAddDocument}
                  >
                    <span className="typography-heading-4">+</span> Add Document
                  </button> */}
                  <input
                    type="file"
                    ref={fileInputRef}
                    accept=".pdf,.doc,.docx,.xls,.xlsx,.png,.jpg,.jpeg,.gif"
                    multiple
                    className="hidden"
                    onChange={handleFileChange}
                  />
                </div>

                {/* Document list with empty states */}
                <div className="flex-1 overflow-y-auto px-4">
                  {isLoading ? (
                    <div className="space-y-4 py-4">
                      <DocumentLoader />
                      <DocumentLoader />
                      <DocumentLoader />
                    </div>
                  ) : documentList.length === 0 ? (
                    <EmptyStateView
                      type="noDocumentsFound"
                      onClick={handleAddDocument}
                    />
                  ) : searchQuery && filteredDocuments.length === 0 ? (
                    <EmptyStateView
                      type="noDocumentSearchResults"
                      onClick={() => handleSearch("")}
                    />
                  ) : (
                    <div className="space-y-4 py-4">
                      {getCurrentPageItems().map((doc) => (
                        <div key={doc.id}>
                          {/* Document entry with better percentage display */}
                          <div className="border rounded-lg p-4">
                            {/* Document header with title and status */}
                            <div className="flex items-center justify-between">
                              {/* Document icon and metadata */}
                              <div className="flex items-center gap-3">
                                <svg
                                  viewBox="0 0 24 24"
                                  className="w-6 h-6 text-primary -mt-4"
                                >
                                  <path
                                    fill="currentColor"
                                    d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8l-6-6zm-1 1v5h5v10H6V4h7z"
                                  />
                                </svg>
                                <div>
                                  <div className="flex gap-3 items-center">
                                    <h3 className="font-weight-medium">{doc.name}</h3>
                                    <div
                                      className={`inline-block bg-semantic-gray-100 text-semantic-gray-600 typography-body-sm px-2 py-0.5 rounded mr-2 ${
                                        doc.status === "processing"
                                          ? "bg-primary-100 text-primary"
                                          : doc.status === "completed"
                                          ? "bg-green-100 text-green-600"
                                          : doc.status === "failed"
                                          ? "bg-red-100 text-red-600"
                                          : "invisible"
                                      }`}
                                    >
                                      {doc.status}
                                    </div>

                                    {doc.tags.map((tag) => (
                                      <span
                                        key={tag}
                                        className="inline-block bg-semantic-gray-100 text-semantic-gray-600 typography-body-sm px-2 py-0.5 rounded -ml-1"
                                      >
                                        {tag}
                                      </span>
                                    ))}
                                  </div>

                                  {/* Document metadata */}
                                  <div className="flex items-center gap-2 typography-body-sm text-semantic-gray-500 mt-2">
                                    <span> {fileTypes[doc.name?.split('.').pop()?.toLowerCase()] ||
                                        doc.name?.split('.').pop()?.toUpperCase() || 'Unknown'}</span>
                                    <span>•</span>
                                    <span>{doc.size}</span>
                                    <span>•</span>
                                    <span>By {doc.author}</span>
                                    <span>•</span>
                                    <span>Modified {doc.lastModified}</span>
                                  </div>
                                </div>
                              </div>

                              {/* Action buttons */}
                              <div className="flex items-center gap-4">
                                <button
                                  onClick={() =>
                                    handleViewDocument(doc.viewUrl, doc.name)
                                  }
                                  title="View document"
                                  className="flex items-center gap-1"
                                >
                                  <Eye className="w-5 h-5 text-semantic-gray-400 hover:text-semantic-gray-600 cursor-pointer" />
                                </button>
                                <a
                                  href={doc.downloadUrl}
                                  download
                                  title="Download document"
                                  className="flex items-center gap-1"
                                  onClick={handleDownload}
                                >
                                  <Download className="w-5 h-5 text-semantic-gray-400 hover:text-semantic-gray-600 cursor-pointer" />
                                </a>
                                <button
                                  onClick={() =>
                                    handleDeleteButtonClick(doc.file_uuid, doc.name)
                                  }
                                  title="Delete document"
                                  className={`flex items-center gap-1 ${
                                    doc.status !== "completed"
                                      ? "opacity-50 cursor-not-allowed"
                                      : ""
                                  }`}
                                  disabled={doc.status !== "completed"}
                                >
                                  <Trash2
                                    className={`w-5 h-5 ${
                                      doc.status === "completed"
                                        ? "text-semantic-gray-400 hover:text-red-600 cursor-pointer"
                                        : "text-semantic-gray-300 cursor-not-allowed"
                                    }`}
                                  />
                                </button>
                              </div>
                            </div>

                            {/* Progress display */}
                            {doc.status && (
                              <>
                                {doc.status === "processing" && (
                                  <div className="mt-2 relative">
                                    {/* Display percentage at the top right of the progress bar */}
                                    <div className="flex justify-between items-center mb-1">
                                      <span className="typography-body-sm text-white">
                                        Processing...
                                      </span>
                                      <span className="typography-body-sm font-weight-medium text-primary">
                                        {Math.round(
                                          doc.percentage || doc.progress
                                        )}
                                        %
                                      </span>
                                    </div>
                                    <div className="w-full bg-semantic-gray-200 rounded-full h-2">
                                      <div
                                        className="h-2 rounded-full bg-primary"
                                        style={{
                                          width: `${
                                            doc.percentage || doc.progress
                                          }%`,
                                          animation:
                                            "progress-pulse 1.5s ease-in-out infinite",
                                        }}
                                      />
                                    </div>
                                    {/* Add blinking effect for remaining time */}
                                    {doc.remainingTime && (
                                      <div className="mt-1 text-right">
                                        <span className="typography-caption text-semantic-gray-600 blink-animation">
                                          Estimated time remaining:{" "}
                                          {doc.remainingTime}
                                        </span>
                                      </div>
                                    )}
                                  </div>
                                )}
                                {doc.status !== "processing" && (
                                  <div className="mt-3">
                                    <div className="w-full bg-semantic-gray-200 rounded-full h-2">
                                      <div
                                        className={`h-2 rounded-full ${
                                          doc.status === "completed"
                                            ? "bg-green-500"
                                            : "bg-red-500"
                                        }`}
                                        style={{ width: "100%" }}
                                      />
                                    </div>
                                  </div>
                                )}
                              </>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Show pagination only when there are documents */}
                {documentList.length > 0 && (
                  <div className="">
                    <div className="px-4 py-3">
                      <Pagination
                        currentPage={currentPage}
                        pageCount={Math.ceil(
                          (searchQuery
                            ? filteredDocuments.length
                            : documentList.length) / pageSize
                        )}
                        pageSize={pageSize}
                        totalItems={
                          searchQuery
                            ? filteredDocuments.length
                            : documentList.length
                        }
                        onPageChange={setCurrentPage}
                        onPageSizeChange={setPageSize}
                        pageSizeOptions={[5, 10, 15, 20]}
                      />
                    </div>
                  </div>
                )}
              </div>
            )}

            {activeTab === "design" && <EmptyStateView type="comingSoon" />}

            {(activeTab === "code" || isCodeOpen === true) && (
              <RepositoryList
                isRepoModalOpen={isRepoModalOpen}
                setIsRepoModalOpen={setIsRepoModalOpen}
                setCodeLength={setCodeLength}
                setActiveTab={setActiveTab}
                setTabCounts={setTabCounts}
                handleCloseAsset={handleCloseAsset}
              />
            )}
          </>
        </div>
      </div>
      <DeleteProjectModal
        isOpen={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setDocumentToDelete(null);
        }}
        onDelete={handleDeleteDocument}
        isDeleting={isDeleting}
        type="document"
      />
      {
        viewerModal?.isOpen &&
        <FileViewerModal
          isOpen={viewerModal.isOpen}
          onClose={closeViewer}
          file={viewerModal.file}
          viewUrl={viewerModal.viewUrl}
        />
      }
    </div>
  );
};

export default ProjectAssets;
