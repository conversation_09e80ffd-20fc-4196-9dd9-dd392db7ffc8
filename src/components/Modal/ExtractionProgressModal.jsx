import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>aSearch, FaMagic, <PERSON>a<PERSON><PERSON><PERSON> } from 'react-icons/fa';
import { X } from 'lucide-react';

const LoadingStep = ({ label, status, icon: Icon, progress }) => (
  <div className="flex items-center space-x-4 py-3">
    <div className={`
      rounded-full p-3 transition-all duration-300
      ${status === 'completed' ? 'bg-green-100' :
        status === 'loading' ? 'bg-primary-100' :
          'bg-semantic-gray-100'}
    `}>
      {status === 'completed' ? (
        <FaCheck className="h-5 w-5 text-green-600" />
      ) : status === 'loading' ? (
        <Icon className={`h-5 w-5 ${status === 'loading' ? 'text-primary animate-pulse' : 'text-semantic-gray-400'}`} />
      ) : (
        <Icon className="h-5 w-5 text-semantic-gray-400" />
      )}
    </div>
    <div className="flex-1">
      <p className={`font-weight-medium ${status === 'completed' ? 'text-green-600' :
          status === 'loading' ? 'text-primary' :
            'text-semantic-gray-500'
        }`}>
        {label}
        {progress && (
          <span className="ml-2 typography-body-sm text-semantic-gray-500">
            ({progress.filesProcessed}/{progress.totalFiles} files)
          </span>
        )}
      </p>
      <div className="mt-1 h-1 w-full rounded-full bg-semantic-gray-100">
        {(status === 'completed' || status === 'loading') && (
          <div
            className={`h-1 rounded-full transition-all duration-500 ${status === 'completed' ? 'bg-green-500 w-full' :
                'bg-primary w-3/4 animate-pulse'
              }`}
          />
        )}
      </div>
    </div>
  </div>
);

const ExtractionProgressModal = ({handleModalClose, currentStep, progress}) => {
    const initializationSteps = [
        { label: 'Initializing session for extraction', id: 'init', icon: FaCode },
        { label: 'Analyzing your codebase...', id: 'analyze', icon: FaSearch },
        { label: 'Almost there! Your code analysis will be ready for queries in a few moments...', id: 'finalize', icon: FaMagic }
    ];

    return (
        <div className="fixed inset-0 bg-semantic-gray-800 bg-opacity-25 flex justify-center items-center z-50">
          <div className="bg-white rounded-2xl shadow-2xl w-full max-w-2xl mx-4 p-8">
            <div className="flex justify-end">
                <button 
                    onClick={handleModalClose} 
                    className="p-2 rounded-md hover:bg-semantic-gray-100 hover:text-red-500 transition-colors"
                    aria-label="Close modal"
                >
                    <X size={16} />
                </button>
            </div>
            <div className="text-center mb-8">
              <h2 className="typography-heading-2 font-weight-bold text-semantic-gray-900">Initializing Extraction</h2>
              <p className="mt-2 text-semantic-gray-600">
                Please wait while we complete the extraction
              </p>
            </div>

            <div className="space-y-6 mt-8">
              {initializationSteps.map((step, index) => (
                <LoadingStep
                  key={step.id}
                  label={step.label}
                  icon={step.icon}
                  status={
                    index < currentStep ? 'completed' :
                      index === currentStep ? 'loading' :
                        'pending'
                  }
                  progress={index === 1 ? progress : null}
                />
              ))}
            </div>
          </div>
        </div>
    )
}

export default ExtractionProgressModal;