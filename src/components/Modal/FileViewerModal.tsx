import { X, ZoomIn, ZoomOut } from "lucide-react";
import React, { useState } from "react";

export const FileViewerModal = ({ isOpen, onClose, file, viewUrl }: any) => {
    const [zoom, setZoom] = useState(100);

    if (!isOpen) return null;
    const getFileExtension = (filename: any) => {
        return filename?.split('.').pop()?.toLowerCase() || '';
    };

    const renderFileContent = () => {
        const extension = getFileExtension(file?.name || '');

        switch (extension) {
            case 'jpg':
            case 'jpeg':
            case 'png':
            case 'gif':
            case 'bmp':
            case 'svg':
                return (
                    <div className="flex rounded items-center justify-center h-full bg-semantic-gray-100">
                        <img
                            src={viewUrl}
                            alt={file?.name}
                            className="max-w-full max-h-full object-contain"
                            style={{ transform: `scale(${zoom / 100})` }}
                        />
                    </div>
                );

            case 'txt':
            case 'md':
            case 'json':
            case 'xml':
            case 'csv':
                return (
                    <div className="h-full rounded overflow-auto p-4 bg-semantic-gray-50">
                        <TextFileViewer url={viewUrl} />
                    </div>
                );

            case 'mp4':
            case 'webm':
            case 'ogg':
                return (
                    <div className="flex rounded items-center justify-center h-full bg-black">
                        <video
                            src={viewUrl}
                            controls
                            className="max-w-full max-h-full"
                        >
                            Your browser does not support the video tag.
                        </video>
                    </div>
                );

            case 'mp3':
            case 'wav':
            case 'ogg':
                return (
                    <div className="flex rounded items-center justify-center h-full bg-semantic-gray-100">
                        <div className="text-center">
                            <div className="mb-4">
                                <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-2">
                                    <span className="text-white text-2xl">🎵</span>
                                </div>
                                <p className="text-semantic-gray-600 mb-4">{file?.name}</p>
                            </div>
                            <audio
                                src={viewUrl}
                                controls
                                className="w-full max-w-md"
                            >
                                Your browser does not support the audio tag.
                            </audio>
                        </div>
                    </div>
                );

            default:
                return (
                    <div className="flex rounded items-center justify-center h-full bg-semantic-gray-100">
                        <div className="text-center">
                            <div className="w-16 h-16 bg-semantic-gray-400 rounded-full flex items-center justify-center mx-auto mb-4">
                                <span className="text-white text-2xl">📄</span>
                            </div>
                            <p className="text-semantic-gray-600 mb-4">Cannot preview this file type</p>
                            <p className="text-sm text-semantic-gray-500">File: {file?.name}</p>
                            <button
                                onClick={() => window.open(viewUrl, '_blank')}
                                className="mt-4 px-4 py-2 bg-primary text-white rounded hover:bg-primary-600"
                            >
                                Open in New Tab
                            </button>
                        </div>
                    </div>
                );
        }
    };

    const isImage = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg'].includes(
        getFileExtension(file?.name || '')
    );

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded w-11/12 h-5/6 max-w-6xl max-h-5xl flex flex-col">
                {/* Header */}
                <div className="flex items-center justify-between p-4 border-b">
                    <div className="flex items-center gap-2">
                        <h3 className="text-lg font-semibold truncate">{file?.name}</h3>
                        <span className="text-sm text-semantic-gray-500 bg-semantic-gray-100 px-2 py-1 rounded">
                            {getFileExtension(file?.name || '').toUpperCase()}
                        </span>
                    </div>

                    <div className="flex items-center gap-2">
                        {/* Zoom controls for images */}
                        {isImage && (
                            <>
                                <button
                                    onClick={() => setZoom(Math.max(25, zoom - 25))}
                                    className="p-2 hover:bg-semantic-gray-100 rounded"
                                    title="Zoom Out"
                                >
                                    <ZoomOut className="w-4 h-4" />
                                </button>
                                <span className="text-sm text-semantic-gray-600 min-w-12 text-center">
                                    {zoom}%
                                </span>
                                <button
                                    onClick={() => setZoom(Math.min(200, zoom + 25))}
                                    className="p-2 hover:bg-semantic-gray-100 rounded"
                                    title="Zoom In"
                                >
                                    <ZoomIn className="w-4 h-4" />
                                </button>
                            </>
                        )}

                        {/* Close button */}
                        <button
                            onClick={onClose}
                            className="p-2 hover:bg-semantic-gray-100 rounded"
                            title="Close"
                        >
                            <X className="w-4 h-4" />
                        </button>
                    </div>
                </div>

                {/* Content */}
                <div className="flex-1 overflow-hidden">
                    {renderFileContent()}
                </div>
            </div>
        </div>
    );
};

export const TextFileViewer = ({ url }: any) => {
    const [content, setContent] = useState('Loading...');

    React.useEffect(() => {
        fetch(url)
            .then(response => response.text())
            .then(text => setContent(text))
            .catch(error => setContent('Error loading file content'));
    }, [url]);

    return (
        <pre className="whitespace-pre-wrap text-sm font-mono bg-white p-4 rounded border">
            {content}
        </pre>
    );
};