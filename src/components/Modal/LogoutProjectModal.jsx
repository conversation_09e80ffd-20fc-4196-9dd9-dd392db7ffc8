import React from 'react';
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import { LogOut, AlertCircle, X } from 'lucide-react';

const LogoutProjectModal = ({ setIsLogoutModalOpen, handleLogout }) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full">
        {/* Close button */}
        <button
          onClick={() => setIsLogoutModalOpen(false)}
          className="absolute right-4 top-4 p-1 rounded-full hover:bg-semantic-gray-200 transition-all duration-300 hover:shadow"
        >
          <X className="w-5 h-5 text-semantic-gray-500 hover:text-semantic-gray-700" />
        </button>

        {/* Modal content */}
        <div className="p-6">
          {/* Icon */}
          <div className="flex justify-center mb-4">
            <div className="p-3 bg-red-100 rounded-full">
              <AlertCircle className="h-6 w-6 text-red-600" />
            </div>
          </div>

          {/* Title */}
          <h3 className="typography-body-lg font-weight-semibold text-center text-semantic-gray-900 mb-2">
            Confirm Logout
          </h3>

          {/* Description */}
          <p className="text-center text-semantic-gray-500 mb-6">
            Are you sure you want to log out of your account?
            You&apos;ll need to sign in again to access your dashboard.
          </p>

          {/* Buttons */}
          <div className="flex flex-col-reverse sm:flex-row justify-center gap-3">
            <DynamicButton
              variant="secondary"
              onClick={() => setIsLogoutModalOpen(false)}
              text="Cancel"
              className="hover:scale-105"
            />
            <DynamicButton
              variant="danger"
              icon={LogOut}
              onClick={handleLogout}
              text="Logout"
              className="hover:scale-105"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default LogoutProjectModal;