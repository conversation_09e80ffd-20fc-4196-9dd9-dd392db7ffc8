import React from 'react';
import { X as CloseIcon } from 'lucide-react';
import RepositoryConfigurationPage from '@/components/Repository/RepositoryConfigurationPage';

/**
 * RepositoryModal - A simplified modal that uses the consolidated RepositoryConfigurationPage
 * This modal serves as a container for the RepositoryConfigurationPage component
 */
const RepositoryDetailsModal = ({ open, onClose, projectId, containerId, onSuccess, handleRepoChange }) => {
  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        {/* Backdrop */}
        <div
          className="fixed inset-0 backdrop-blur-modal transition-opacity bg-black/50 dark:bg-black/60"
          onClick={onClose}
        />

        {/* Modal */}
        <div className="relative w-[40rem] max-h-[calc(100vh-4rem)] bg-white rounded-lg shadow-2xl flex flex-col">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-semantic-gray-200 flex-shrink-0">
            <h2 className="typography-body-lg font-weight-semibold text-semantic-gray-900">
              Repository Configuration
            </h2>
            <button
              onClick={onClose}
              className="p-1 text-semantic-gray-500 hover:text-semantic-gray-700 transition-colors"
            >
              <CloseIcon className="h-5 w-5" />
            </button>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto">
            <div className="p-6">
              <RepositoryConfigurationPage
                projectId={projectId}
                containerId={containerId}
                onSuccess={onSuccess}
                handleRepoChange={handleRepoChange}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RepositoryDetailsModal;