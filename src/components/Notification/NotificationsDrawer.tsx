import { Bell } from "lucide-react"
import { PanelProvider } from "../Context/PanelContext";
import NotificationList from "../Notification/NotificationList";
import Drawer from "../Drawer";

import '@/styles/notificationlist.css';

interface NotificationsDrawerProps {
    isOpen: boolean;
    onClose: () => void;
    onClearAll: () => void;
    handleValueChange: (value: number) => void;
    theme?: 'light' | 'dark';
}

export const NotificationsDrawer: React.FC<NotificationsDrawerProps> = ({
    isOpen,
    onClose,
    onClearAll,
    handleValueChange,
    theme = 'light',
}) => {
    const themeClasses = {
        light: {
            title: "text-semantic-gray-900",
            icon: "text-semantic-gray-700",
        },
        dark: {
            title: "text-semantic-gray-100",
            icon: "text-semantic-gray-300",
        }
    };

    return (
        <PanelProvider>
            <Drawer
                isOpen={isOpen}
                onClose={onClose}
                placement="left"
                showBackdrop={false}
                theme={theme}
                title={
                    <span className="drawer-title">
                        <div className="drawer-title-container">
                        <Bell className={`drawer-title-icon ${themeClasses[theme].icon}`} />
                            <span className={`drawer-title-name ${themeClasses[theme].title}`}>Notifications</span>
                        </div>
                    </span>
                }
                width={400}
            >
                <NotificationList
                    handleValueChange={handleValueChange}
                    handleDrawerToggle={onClose}
                    handleClearAll={onClearAll}
                    theme={theme}
                />
            </Drawer>
        </PanelProvider>
    );
};