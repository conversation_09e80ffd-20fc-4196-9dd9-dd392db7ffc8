"use client";
import { useEffect, useState } from "react";

export const NotificationSettings = () => {
  const [settings, setSettings] = useState({
    pushEnabled: true,
    emailEnabled: true, 
  });

  const [permission, setPermission] = useState(Notification.permission);

  // Recheck browser permission and update toggle state
  const recheckPermission = () => {
    const currentPermission = Notification.permission;
    setPermission(currentPermission);

    if (currentPermission === "denied") {
      setSettings((prev) => ({ ...prev, pushEnabled: false }));
      localStorage.setItem("pushEnabled", "false");
    } else if (currentPermission === "granted") {
      setSettings((prev) => ({ ...prev, pushEnabled: true }));
      localStorage.setItem("pushEnabled", "true");
    }
  };

  // Run on first load
  useEffect(() => {
    recheckPermission();
  }, []);

  // Handle user-controlled toggle
  const updateSettings = async (newSettings) => {
    setSettings(newSettings);
    localStorage.setItem("pushEnabled", newSettings.pushEnabled.toString());

  };

  return (
    <div className="p-4 bg-white rounded-lg shadow">
      <h2 className="typography-heading-4 font-weight-bold mb-4">
        Notification Settings
      </h2>

      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <span>Push Notifications</span>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={settings.pushEnabled}
              onChange={(e) =>
                updateSettings({
                  ...settings,
                  pushEnabled: e.target.checked,
                })
              }
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-semantic-gray-200 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-semantic-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
          </label>
        </div>
      </div>
    </div>
  );
};
