import React, { useState, useRef, useEffect, useContext } from 'react';
import { ChevronDown, Loader2, G<PERSON><PERSON>, <PERSON>, CheckCircle2 } from 'lucide-react';
import { getCookie } from '@/utils/auth';
import { getHeadersRaw } from '@/utils/api';
import ProjectAssetPagination from '../UIComponents/Paginations/ProjectAssetPagination';
import { AlertContext } from '../NotificationAlertService/AlertList';
import { TableLoadingSkeleton } from "@/components/UIComponents/Loaders/LoaderGroup";

interface Branch {
  name: string;
  isDefault: boolean;
}

interface Repository {
  id: number;
  name: string;
  description: string | null;
  languages: string[];
  branch: string | null;
  branches: Branch[];
  selected: boolean;
  lastUpdated: string;
  clone_url: string;
  path?: string;
  repo_type?: 'public' | 'private';
  full_name?: string;
  default_branch?: string;
  html_url?: string;
  private?: boolean;
  owner?: {
    login: string;
  };
  isImported?: boolean; // New field to track imported status
}

interface RepoImportTableProps {
  repositories: Repository[];
  onToggleRepository: (index: number) => void;
  onBranchChange: (index: number, updates: any) => void;
  selectedCount: number;
  onSelectAll: (checked: boolean) => void;
  isLoading?: boolean;
  activeTabVal?: string;
  organization_name?: string;
  connectedUsername?: string | null;
  importedRepositories?: Array<{
    id: number;
    name: string;
    full_name: string;
    branch: string;
    repo_type: 'public' | 'private';
    html_url?: string;
    description?: string | null;
    importedAt: string;
  }>; // Imported repositories from parent
}

interface BranchSelectProps {
  repo: Repository;
  index: number;
  onBranchSelect: (branch: string) => void;
  isSelectable?: boolean;
  onUpdateRepository: (index: number, updates: any) => void;
  isActive: boolean;
  onDropdownToggle: (index: number) => void;
  onDropdownClose: () => void;
  tableRef: React.RefObject<HTMLElement | null>;
  activeTabVal?: string;
  organization_name?: string;
  connectedUsername?: string | null;
}

const BranchSelect: React.FC<BranchSelectProps> = ({ 
  repo, 
  index, 
  onBranchSelect, 
  isSelectable = true,
  onUpdateRepository,
  isActive,
  onDropdownClose,
  onDropdownToggle,
  tableRef,
  activeTabVal,
  organization_name,
  connectedUsername
}) => {
  const [branches, setBranches] = useState<Branch[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
const [selectedBranch, setSelectedBranch] = useState<string | null>(
  repo.branch || repo.default_branch || null
);
  const dropdownButtonRef = useRef<HTMLDivElement | null>(null);
  const branchDropdownRef = useRef<HTMLDivElement | null>(null);
  const [menuPosition, setMenuPosition] = useState({top: 0, left: 0});
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [filteredBranches, setFilteredBranches] = useState<Branch[]>([]);

  useEffect(() => {
    const updateDropdownPosition = () => {
      const rect = dropdownButtonRef?.current?.getBoundingClientRect();
      if(rect && branchDropdownRef.current) {
        setMenuPosition({top: rect.bottom + window.scrollY, left: rect.left - 100 + window.scrollX})
      }
    }
    
    const closeMenu = (e: MouseEvent | Event) => {
      if(dropdownButtonRef.current && branchDropdownRef.current && e.target instanceof HTMLElement){
        if(!dropdownButtonRef.current.contains(e.target) && !branchDropdownRef.current.contains(e.target)){
          onDropdownClose();
        }
      }
    }
    const tableContainer = tableRef?.current;

    if(isActive){
      updateDropdownPosition();
      tableContainer?.addEventListener("scroll", closeMenu);
      window.addEventListener("resize", updateDropdownPosition);
      document.addEventListener("mousedown", closeMenu);
    }

    return(() => {
      tableContainer?.removeEventListener("scroll", closeMenu);
      window.removeEventListener("resize", updateDropdownPosition);
      document.removeEventListener("mousedown", closeMenu);
    })
  }, [isActive])

  useEffect(() => {
    setFilteredBranches(
      branches.filter((branch) => 
        branch.name.toLowerCase().includes(searchTerm.toLowerCase())
      )
    )
  }, [searchTerm, branches])

  const fetchBranches = async () => {
    // Extract owner with proper priority
    let owner = '';
    
    // Priority 1: Use connected username if available (for connected repositories)
    if (connectedUsername) {
      owner = connectedUsername;
    }
    // Priority 2: Direct owner object
    else if (repo.owner?.login) {
      owner = repo.owner.login;
    } 
    // Priority 3: Extract from full_name
    else if (repo.full_name && repo.full_name.includes('/')) {
      owner = repo.full_name.split('/')[0];
    } 
    // Priority 4: Extract from clone_url
    else if (repo.clone_url) {
      try {
        const url = new URL(repo.clone_url);
        const pathParts = url.pathname.split('/').filter(part => part && part !== '.git');
        if (pathParts.length >= 2) {
          owner = pathParts[0];
        }
      } catch (e) {
        console.error('Error parsing clone_url:', e);
      }
    }
    // Priority 5: Extract from html_url or path  
    else if (repo.html_url || repo.path) {
      const urlToUse = repo.html_url || repo.path || '';
      
      try {
        const url = new URL(urlToUse);
        const pathParts = url.pathname.split('/').filter(part => part);
        
        if (pathParts.length >= 2) {
          owner = pathParts[0];
          
        }
      } catch (e) {
        console.error('URL parsing failed, trying string split:', e);
        // If URL parsing fails, try simple string splitting
        const parts = urlToUse.split('/').filter(part => part && part !== 'https:' && part !== 'http:');
        
        const githubIndex = parts.findIndex(part => part === 'github.com');
        if (githubIndex !== -1 && githubIndex + 1 < parts.length) {
          owner = parts[githubIndex + 1];
          
        } else if (parts.length >= 4) {
          // Fallback: assume pattern like ["github.com", "owner", "repo"]
          owner = parts[1];
          
        }
      }
    }

    

    if (!owner || owner === 'unknown') {
      setError('Unable to determine repository owner');
      console.error('Failed to extract owner from repository:', repo);
      return;
    }

    setIsLoading(true);
    setError(null);
    
    try {
      const userId = await getCookie('userId');
      const headers = getHeadersRaw();
      
      const requestPayload = {
        user_id: userId,
        owner: owner,
        repo_name: repo.name,
        org: activeTabVal === 'organization' ? organization_name : false
      };

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/oauth/github/repo-branches`, {
        method: 'POST',
        headers: {
          ...headers,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestPayload),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to fetch branches: ${response.status} ${errorText}`);
      }
      
      const data: Branch[] = await response.json();
      
      if (!Array.isArray(data) || data.length === 0) {
        // If no branches returned, create a default branch
        const defaultBranch = repo.default_branch || 'main';
        const defaultBranchObj = { name: defaultBranch, isDefault: true };
        setBranches([defaultBranchObj]);
        if (!selectedBranch) {
          handleBranchSelect(defaultBranch);
        }
        return;
      }
      
      setBranches(data);

      // If no branch is selected, choose the default branch
      if (!selectedBranch) {
        const defaultBranch = data.find(branch => branch.isDefault) || data[0];
        if (defaultBranch) {
          handleBranchSelect(defaultBranch.name);
        }
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      
      // Fallback to default branch
      const defaultBranch = repo.default_branch || 'main';
      const defaultBranchObj = { name: defaultBranch, isDefault: true };
      setBranches([defaultBranchObj]);
      if (!selectedBranch) {
        handleBranchSelect(defaultBranch);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleBranchSelect = (branchName: string) => {
    setSelectedBranch(branchName);
    onBranchSelect(branchName);
    onUpdateRepository(index, { 
      ...repo,
      branch: branchName
    });
  };

  const handleButtonClick = () => {
    if (!isActive && branches.length === 0) {
      fetchBranches();
    }
    onDropdownToggle(index);
  };

  return (
    <div className="w-[200px]" ref={dropdownButtonRef}>
      <button
        onClick={handleButtonClick}
        disabled={!isSelectable || repo.isImported}
        className={`w-full flex items-center justify-between px-3 py-1.5 border rounded-md text-sm
          ${!isSelectable || repo.isImported
            ? 'bg-semantic-gray-100 cursor-not-allowed border-semantic-gray-200' 
            : 'bg-white border-semantic-gray-300 hover:bg-semantic-gray-50'
          }`}
      >
        <span className={`truncate max-w-[150px] ${!isSelectable || repo.isImported ? 'text-semantic-gray-400' : 'text-black'}`}>
          {selectedBranch || repo.default_branch || 'Select branch'}
        </span>
        <div className="flex items-center gap-1">
          {repo.isImported && (
            <CheckCircle2 className="w-3 h-3 text-green-500 flex-shrink-0" />
          )}
          <ChevronDown
            className={`w-4 h-4 transition-transform flex-shrink-0
              ${isActive ? 'transform rotate-180' : ''} 
              ${!isSelectable || repo.isImported ? 'text-semantic-gray-400' : 'text-semantic-gray-500'}`}
          />
        </div>
      </button>

      {isActive && !repo.isImported && (
        <div 
          ref={branchDropdownRef} 
          className="fixed z-20 w-[300px] mt-1 bg-white border border-semantic-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto"
          style={{ top: `${menuPosition.top}px`, left: `${menuPosition.left}px` }}
        >
          {isLoading ? (
            <div className="flex items-center justify-center p-4">
              <Loader2 className="w-5 h-5 text-orange-500 animate-spin" />
              <span className="ml-2 text-semantic-gray-600">Loading branches...</span>
            </div>
          ) : error ? (
            <div className="p-4 text-red-500 text-center text-sm">{error}</div>
          ) : (
            <>
              {branches.length >= 20 && (
                <div className="sticky top-0 w-full p-2 bg-white border-b">
                  <input 
                    className="rounded-md border border-semantic-gray-300 w-full px-2 py-1 text-sm text-black placeholder-semantic-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500"
                    value={searchTerm}
                    placeholder="Search Branch..."
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              )}
              {filteredBranches.length === 0 ? (
                <div className="p-4 text-semantic-gray-500 text-center text-sm">No branches found</div>
              ) : (
                filteredBranches.map((branch, idx) => (
                  <button
                    key={idx}
                    onClick={() => {
                      if (isSelectable && !repo.isImported) {
                        handleBranchSelect(branch.name);
                      }
                      onDropdownToggle(index);
                    }}
                    className={`w-full px-4 py-2 text-left hover:bg-semantic-gray-100 focus:outline-none focus:bg-semantic-gray-100 text-sm
                      ${selectedBranch === branch.name ? 'bg-orange-50' : ''}`}
                  >
                    <div className="flex items-center justify-between">
                      <span className="text-black">{branch.name}</span>
                      {branch.isDefault && (
                        <span className="text-xs text-orange-600 bg-orange-100 px-2 py-0.5 rounded">
                          default
                        </span>
                      )}
                    </div>
                  </button>
                ))
              )}
            </>
          )}
        </div>
      )}
    </div>
  );
};

const RepoImportTable: React.FC<RepoImportTableProps> = ({ 
  repositories, 
  onToggleRepository,
  onBranchChange,
  selectedCount,
  onSelectAll,
  isLoading = false,
  activeTabVal,
  organization_name,
  connectedUsername,
  importedRepositories = []
}) => {
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeDropdown, setActiveDropdown] = useState<number | null>(null);
  const [showImportedOnly, setShowImportedOnly] = useState(false);
  const tableRef = useRef<HTMLDivElement | null>(null);
  const { showAlert } = useContext(AlertContext);

  // Mark repositories as imported if they exist in importedRepositories
  const repositoriesWithImportStatus = repositories.map(repo => {
    const isImported = importedRepositories.some(imported => {
      // Check by ID first, then by full_name, then by name
      return imported.id === repo.id || 
             imported.full_name === repo.full_name ||
             imported.full_name === `${repo.owner?.login}/${repo.name}` ||
             (imported.name === repo.name && imported.full_name.includes(repo.name));
    });
    
    return {
      ...repo,
      isImported
    };
  });

  const filteredRepositories = repositoriesWithImportStatus.filter((repo) => {
    const matchesSearch = repo.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (repo.description && repo.description.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (repo.full_name && repo.full_name.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesImportFilter = showImportedOnly ? repo.isImported : true;

    return matchesSearch && matchesImportFilter;
  });

  const getCurrentPageItems = () => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return filteredRepositories.slice(startIndex, endIndex);
  };

  const handleToggleRepository = (pageIndex: number) => {
    const actualIndex = (currentPage - 1) * pageSize + pageIndex;
    const originalRepo = filteredRepositories[actualIndex];
    
    // Don't allow toggling of selected repositories
    if (originalRepo.isImported) {
      showAlert('This repository has already been selected', 'warning');
      return;
    }

    const trueOriginalIndex = repositories.findIndex(
      (repo) => repo.id === originalRepo.id
    );
    onToggleRepository(trueOriginalIndex);
  };

  const handleSelectAll = (checked: boolean) => {
    // Only allow selecting non-imported repositories
    if (checked) {
      const availableRepos = repositoriesWithImportStatus.filter(repo => !repo.isImported);
      if (availableRepos.length === 0) {
        showAlert('No repositories available for selection', 'warning');
        return;
      }
    }
    onSelectAll(checked);
  };

  const handleBranchUpdate = (pageIndex: number, updates: any) => {
    const actualIndex = (currentPage - 1) * pageSize + pageIndex;
    const originalRepo = filteredRepositories[actualIndex];
    const trueOriginalIndex = repositories.findIndex(
      (repo) => repo.id === originalRepo.id
    );
    onBranchChange(trueOriginalIndex, updates);
  };

  const availableRepoCount = repositoriesWithImportStatus.filter(repo => !repo.isImported).length;
  const importedRepoCount = repositoriesWithImportStatus.filter(repo => repo.isImported).length;
  const availableSelectedCount = repositoriesWithImportStatus.filter(repo => !repo.isImported && repo.selected).length;
  const filteredAvailableCount = filteredRepositories.filter(repo => !repo.isImported).length;

  if (isLoading) {
    return (
      <div className="flex flex-col h-full">
        {/* Search and Filter Bar Skeleton */}
        <div className="flex items-center justify-between mb-4 gap-4">
          <div className="flex items-center gap-4 flex-1">
            <div className="flex-1 max-w-md h-10 bg-semantic-gray-200 rounded-md animate-pulse"></div>
          </div>
          <div className="flex items-center gap-4">
            <div className="w-24 h-4 bg-semantic-gray-200 rounded animate-pulse"></div>
            <div className="w-20 h-4 bg-semantic-gray-200 rounded animate-pulse"></div>
          </div>
        </div>

        {/* Table Skeleton */}
        <div className="flex-grow relative">
          <div className="flex-grow border border-semantic-gray-200 rounded-md">
            <TableLoadingSkeleton />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Search and Filter Bar */}
      <div className="flex items-center justify-between mb-4 gap-4">
        <div className="flex items-center gap-4 flex-1">
          <input
            type="text"
            placeholder="Search repositories..."
            className="flex-1 max-w-md px-3 py-2 border border-semantic-gray-300 rounded-md text-sm text-black placeholder-semantic-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            value={searchQuery}
            onChange={(e) => {
              setCurrentPage(1);
              setSearchQuery(e.target.value);
            }}
          />
          
          {importedRepoCount > 0 && (
            <div className="flex items-center gap-2">
              <label className="flex items-center gap-2 text-sm text-semantic-gray-600">
                <input
                  type="checkbox"
                  checked={showImportedOnly}
                  onChange={(e) => {
                    setShowImportedOnly(e.target.checked);
                    setCurrentPage(1);
                  }}
                  className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-semantic-gray-300 rounded"
                />
                Show selected only
              </label>
            </div>
          )}
        </div>

        <div className="flex items-center gap-4 text-sm text-semantic-gray-600">
          {availableRepoCount > 0 && (
            <span>
              <span className="font-medium text-semantic-gray-900">{availableRepoCount}</span> available
            </span>
          )}
          {importedRepoCount > 0 && (
            <span>
              <span className="font-medium text-green-600">{importedRepoCount}</span> selected
            </span>
          )}
          {availableSelectedCount > 0 && (
            <span className="text-orange-600 font-medium">
              {availableSelectedCount} to select
            </span>
          )}
        </div>
      </div>

      {/* Table Container */}
      <div className="flex-grow relative">
        <div ref={tableRef} className="flex-grow overflow-hidden flex flex-col max-h-[50vh] overflow-y-auto">
          <div className="flex-grow border border-semantic-gray-200 rounded-md">
            {repositories.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-12">
                <Github className="mx-auto h-12 w-12 text-semantic-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-black">No repositories found</h3>
                <p className="mt-1 text-sm text-semantic-gray-500">
                  No repositories were found in your connected account.
                </p>
              </div>
            ) : getCurrentPageItems().length === 0 ? (
              <div className="flex flex-col items-center justify-center py-12">
                <Github className="mx-auto h-12 w-12 text-semantic-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-black">No repositories match your search</h3>
                <p className="mt-1 text-sm text-semantic-gray-500">Try adjusting your search terms or filters</p>
                <button
                  onClick={() => {
                    setSearchQuery("");
                    setShowImportedOnly(false);
                  }}
                  className="mt-2 text-sm text-orange-600 hover:text-orange-700"
                >
                  Clear filters
                </button>
              </div>
            ) : (
              <table className="w-full border-collapse">
                <thead className="bg-semantic-gray-50 sticky top-0 z-10">
                  <tr>
                    <th className="w-12 py-3 px-4">
                      <input
                        type="checkbox"
                        className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-semantic-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed"
                        onChange={(e) => handleSelectAll(e.target.checked)}
                        checked={availableSelectedCount === availableRepoCount && availableRepoCount > 0}
                        disabled={availableRepoCount === 0 || isLoading}
                      />
                    </th>
                    <th className="text-left py-3 px-4 w-1/3">
                      <span className="text-black text-xs font-medium uppercase tracking-wider">Repository</span>
                    </th>
                    <th className="text-left py-3 px-4 w-1/2">
                      <span className="text-black text-xs font-medium uppercase tracking-wider">Description</span>
                    </th>
                    <th className="text-left py-3 px-4 w-[200px]">
                      <span className="text-black text-xs font-medium uppercase tracking-wider">Branch</span>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {getCurrentPageItems().map((repo, index) => (
                    <tr
                      key={repo.id}
                      className={`border-t border-semantic-gray-200 transition-colors ${
                        repo.isImported 
                          ? 'bg-green-50 border-l-4 border-l-green-500' 
                          : repo.selected 
                          ? 'bg-orange-50 border-l-4 border-l-orange-500 hover:bg-orange-100' 
                          : 'hover:bg-semantic-gray-50'
                      }`}
                    >
                      <td className="py-4 px-4">
                        <input
                          type="checkbox"
                          checked={repo.selected}
                          onChange={() => handleToggleRepository(index)}
                          disabled={repo.isImported}
                          className={`h-4 w-4 text-orange-600 focus:ring-orange-500 border-semantic-gray-300 rounded ${
                            repo.isImported ? 'opacity-50 cursor-not-allowed' : ''
                          }`}
                        />
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex flex-col">
                          <div className="text-sm font-medium text-black flex items-center gap-2">
                            <Github className="w-4 h-4 text-semantic-gray-600" />
                            {repo.name}
                            {repo.isImported && (
                              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                <CheckCircle2 className="w-3 h-3 mr-1" />
                                Selected
                              </span>
                            )}
                          </div>
                          {repo.full_name && (
                            <div className="text-sm text-semantic-gray-600">
                              {repo.full_name}
                            </div>
                          )}
                          {repo.html_url && (
                            <a
                              href={repo.html_url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-xs text-orange-600 hover:text-orange-700 flex items-center gap-1 mt-1"
                            >
                              <Link className="w-3 h-3" />
                              View on GitHub
                            </a>
                          )}
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="text-sm text-black">
                          {repo.description || (
                            <span className="text-semantic-gray-400 italic">No description available</span>
                          )}
                        </div>
                        {repo.languages && repo.languages.length > 0 && (
                          <div className="flex flex-wrap gap-1 mt-2">
                            {repo.languages.slice(0, 3).map((lang, i) => (
                              <span
                                key={i}
                                className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-semantic-gray-100 text-semantic-gray-800"
                              >
                                {lang}
                              </span>
                            ))}
                            {repo.languages.length > 3 && (
                              <span className="text-xs text-semantic-gray-500">
                                +{repo.languages.length - 3} more
                              </span>
                            )}
                          </div>
                        )}
                      </td>
                      <td className="py-4 px-4">
                        <BranchSelect 
                          repo={repo} 
                          index={index}
                          onBranchSelect={(branchName) => {
                            handleBranchUpdate(index, { ...repo, branch: branchName });
                          }}
                          onUpdateRepository={handleBranchUpdate}
                          isActive={activeDropdown === index}
                          onDropdownToggle={(idx) => {
                            setActiveDropdown(activeDropdown === idx ? null : idx);
                          }}
                          onDropdownClose={() => {
                            setActiveDropdown(null);
                          }}
                          tableRef={tableRef}
                          activeTabVal={activeTabVal || ''}
                          organization_name={organization_name}
                          connectedUsername={connectedUsername}
                        />
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </div>
        </div>

        {/* Pagination */}
        {filteredRepositories.length > 0 && (
          <div className="mt-4">
            <ProjectAssetPagination
              currentPage={currentPage}
              pageCount={Math.ceil(filteredRepositories.length / pageSize)}
              pageSize={pageSize}
              totalItems={filteredRepositories.length}
              onPageChange={setCurrentPage}
              onPageSizeChange={(newPageSize) => {
                setPageSize(newPageSize);
                setCurrentPage(1);
              }}
              pageSizeOptions={[5, 10, 15, 20]}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default RepoImportTable;