// Sandbox.jsx
import { useState, useEffect, useCallback, useRef } from 'react';
import { useSearchParams, useParams } from 'next/navigation';
import { Sandpack, SandpackPreview, SandpackProvider } from '@codesandbox/sandpack-react';
import { getFigmaCodeFiles, mergeChanges } from '@/utils/FigmaAPI';
import { useWebSocket } from "../Context/WebsocketContext";

export default function CodeViewer({ outputPreviewOnly = false }) {
  const {files, setFiles, fileContent, streamingFiles, isCompleted} = useWebSocket();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [merging, setMerging] = useState(false);
  const [mergeStatus, setMergeStatus] = useState(null);
  const searchParams = useSearchParams();
  const params = useParams();
  const figmaDiscussionId = searchParams.get('figmaDiscussionId');
  const selectedDesignId = searchParams.get("selectedDesignId");
  const projectId = params.projectId;
  const designType = searchParams.get("type");
  const fetchFilesRef = useRef(null); // Reference to the fetchFiles function

  // Define fetchFiles function outside useEffect so we can reference it
  const fetchFiles = useCallback(async () => {
    if (!figmaDiscussionId) {
      setError("No discussion ID provided");
      setLoading(false);
      return;
    }

    
    setLoading(true);

    try {
      let response = await getFigmaCodeFiles(figmaDiscussionId);
      if (!response || !response.files || response.files.length === 0) {
        setError("No files found");
        setLoading(false);
        return;
      }

      // Create a properly formatted files object for Sandpack
      const formattedFiles = {};

      // Add all files including design_file.html
      response.files.forEach(file => {
        formattedFiles[file.name] = file.content;
      });

      setFiles(formattedFiles);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching files:', error);
      setError("Failed to load files");
      setLoading(false);
    }
  }, [figmaDiscussionId, setFiles]);

  // Store the fetchFiles function in a ref so we can access it in other effects
  fetchFilesRef.current = fetchFiles;

  // Initial fetch on component mount
  useEffect(() => {
    fetchFiles();
  }, [fetchFiles]);

  // Listen for isCompleted changes to trigger file refresh if needed
  useEffect(() => {
    
    if (isCompleted) {
      

      // Only fetch files from API if we don't have any files yet
      if (!files || Object.keys(files).length === 0) {
        
        setTimeout(() => {
          fetchFiles();
        }, 1000);
      } else {
        
      }
    }
  }, [isCompleted, fetchFiles, files]);

  // Update files when fileContent changes
  useEffect(() => {
    if (Object.keys(fileContent).length > 0) {
      

      // If files is null, initialize it with fileContent
      if (!files) {
        setFiles(fileContent);
      }
      // Otherwise, merge fileContent with existing files
      else {
        setFiles(prevFiles => ({
          ...prevFiles,
          ...fileContent
        }));
      }

      // Set loading to false since we have files
      setLoading(false);
    }
  }, [fileContent, setFiles, files]);

  // Handle the merge changes action
  const handleMergeChanges = async () => {
    if (!projectId || !figmaDiscussionId || !selectedDesignId) {
      setMergeStatus({
        success: false,
        message: "Missing required parameters for merge"
      });
      return;
    }

    setMerging(true);
    try {
      await mergeChanges(
        projectId,
        figmaDiscussionId,
        selectedDesignId,
        designType
      );

      setMergeStatus({
        success: true,
        message: "Changes merged successfully"
      });
    } catch (error) {
      setMergeStatus({
        success: false,
        message: error.message || "Failed to merge changes"
      });
    } finally {
      setMerging(false);

      // Auto-hide the status message after 3 seconds
      setTimeout(() => {
        setMergeStatus(null);
      }, 3000);
    }
  };

// Replace the existing MergeButtonSection component with this enhanced version
const MergeButtonSection = () => (
  <div className="mb-4 flex justify-end items-center gap-3">
    {mergeStatus && (
      <div
        className={`typography-body-sm px-4 py-2 rounded-md font-weight-medium shadow-md transition-all duration-300 transform ${
          mergeStatus.success
            ? 'bg-success/10 text-success border border-success/20'
            : 'bg-destructive/10 text-destructive border border-destructive/20'
        }`}
      >
        <span className="flex items-center">
          {mergeStatus.success ? (
            <svg className="w-4 h-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
          ) : (
            <svg className="w-4 h-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
          )}
          {mergeStatus.message}
        </span>
      </div>
    )}
    <button
      onClick={handleMergeChanges}
      disabled={merging || !files}
      className={`
        px-4 py-2 rounded-md text-primary-foreground font-weight-medium min-w-32 h-10
        flex items-center justify-center gap-2 shadow-sm
        transition-all duration-200 ease-in-out
        focus:outline-none focus:ring-2 focus:ring-offset-2
        ${!files
          ? 'bg-muted cursor-not-allowed opacity-70 text-muted-foreground'
          : merging
            ? 'bg-primary cursor-not-allowed opacity-80'
            : 'bg-primary hover:bg-primary/90 hover:shadow-md active:translate-y-0.5 focus:ring-primary/20'
        }
      `}
    >
      {merging ? (
        <>
          <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span>Merging...</span>
        </>
      ) : (
        <>
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
          </svg>
          <span>Merge Changes</span>
        </>
      )}
    </button>
  </div>
);

  // Render loading state - Still show the button but disabled
  // if (loading) {
  //   return (
  //     <div>
  //       {/* <MergeButtonSection /> */}
  //       <div className="loading-container">
  //         <LoadingSpinner/>
  //       </div>
  //     </div>
  //   );
  // }

  // Render error state - Still show the button but disabled
 
  // if ( !files ||error) {
  //   return (
  //     <div>
  //       {/* <MergeButtonSection /> */}
  //       <div className="flex flex-col items-center justify-center h-64 bg-white rounded-lg border border-dashed border-semantic-gray-300 p-6">
  //         <div className="text-semantic-gray-400 mb-4">
  //           <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
  //             <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/>
  //             <polyline points="14 2 14 8 20 8"/>
  //           </svg>
  //         </div>
  //         <p className="text-semantic-gray-700 font-weight-medium mb-2">{error || "No Files Available"}</p>
  //         <p className="typography-body-sm text-semantic-gray-500 text-center">No code files have been generated yet. They will appear here once available.</p>
  //       </div>
  //     </div>
  //   );
  // }

  // Preview-only mode
  if (outputPreviewOnly) {
    return (
      <div>
        {/* Streaming indicator */}
        {/* {Object.keys(streamingFiles).length > 0 && (
          <div className="fixed top-4 right-4 bg-primary text-white px-3 py-1 rounded-full shadow-lg z-50 flex items-center">
            <svg className="animate-spin h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span className="typography-body-sm font-weight-medium">Streaming files...</span>
          </div>
        )} */}

        <div className="preview-container" style={{ height: "80vh" }}>
          <SandpackProvider
            template="static"
            files={files}
            theme="light"
            options={{
              externalResources: [],
            }}
          >
            <SandpackPreview
              showRefreshButton={true}
              showOpenInCodeSandbox={false}
              style={{
                height: "80vh",
                width: "100%",
                border: "none",
                borderRadius: "0",
              }}
            />
          </SandpackProvider>
        </div>
      </div>
    );
  }

  // Editor and preview mode
  return (
    <div>
      {/* Streaming indicator */}
      {/* {Object.keys(streamingFiles).length > 0 && (
        <div className="fixed top-4 right-4 bg-primary text-white px-3 py-1 rounded-full shadow-lg z-50 flex items-center">
          <svg className="animate-spin h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span className="typography-body-sm font-weight-medium">Streaming files...</span>
        </div>
      )} */}

      <div className="code-tab-container" style={{ height: "80vh" }}>
        <Sandpack
          template="static"
          files={files}
          options={{
            externalResources: [],
            showNavigator: true,
            showLineNumbers: true,
            showInlineErrors: true,
            editorHeight: "80vh",
            activeFile: "index.html"
          }}
          customStyle={{
            width: "100%",
            height: "100%",
            border: "none",
            borderRadius: "0",
            margin: 0,
            padding: 0,
          }}
          theme="light"
        />
      </div>
    </div>
  );
}