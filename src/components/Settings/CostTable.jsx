// components/CostTable.jsx
"use client";
import React, { useState, useEffect } from "react";
import { getLLmCost, fetchNodePropertiesById } from "@/utils/api";
import { formatNumber } from "@/utils/formatting";
import Cookies from "js-cookie";
import { Loading2 } from "../Loaders/Loading";
import { BootstrapTooltip } from "@/components/UIComponents/ToolTip/Tooltip-material-ui";

import EmptyStateView from "../Modal/EmptyStateModal";

const CostTable = () => {
  const [expandedStates, setExpandedStates] = useState(new Set());
  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(true);
  const [llmCostData, setLlmCostData] = useState(null);
  const [projectTitles, setProjectTitles] = useState({});
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(20);
  const rowsPerPageOptions = [5, 10, 20];

  // Get current date info

  const multiplyDollarAmount = (costString) => {
    const amount = parseFloat(costString.replace("$", ""));
    return `$${(amount * 10).toFixed(6)}`;
  };

  const currentDate = new Date();
  const [selectedMonth, setSelectedMonth] = useState(
    currentDate.getMonth() + 1
  );
  const [selectedYear, setSelectedYear] = useState(currentDate.getFullYear());

  // Create array of months for dropdown
  const months = [
    { value: 0, label: "All Months" },
    { value: 1, label: "January" },
    { value: 2, label: "February" },
    { value: 3, label: "March" },
    { value: 4, label: "April" },
    { value: 5, label: "May" },
    { value: 6, label: "June" },
    { value: 7, label: "July" },
    { value: 8, label: "August" },
    { value: 9, label: "September" },
    { value: 10, label: "October" },
    { value: 11, label: "November" },
    { value: 12, label: "December" },
  ];

  // Create array of years (current year and previous 2 years)
  const years = Array.from(
    { length: 3 },
    (_, i) => currentDate.getFullYear() - i
  ).sort((a, b) => b - a);

  // Pagination helpers
  const getPaginatedData = (data) => {
    if (!data) return [];
    const startIndex = (currentPage - 1) * rowsPerPage;
    return data.slice(startIndex, startIndex + rowsPerPage);
  };

  const getTotalPages = (data) => {
    return Math.ceil(data.length / rowsPerPage);
  };

  const loadProjectTitles = async (projects) => {
    const titles = {};
    for (const project of projects) {
      try {
        const projectDetails = await fetchNodePropertiesById(
          project.project_id,
          "Project"
        );

        if (!projectDetails) {
          titles[project.project_id] = "Untitled Project";
          continue;
        }

        titles[project.project_id] =
          projectDetails?.properties?.Title ||
          projectDetails?.ui_metadata?.Title?.value ||
          (projectDetails?.properties?.Description
            ? `${projectDetails.properties.Description.slice(0, 30)}...`
            : "Untitled Project");
      } catch (error) {
        
        titles[project.project_id] = "Untitled Project";
      }
    }
    setProjectTitles(titles);
  };

  const loadLLMCost = async (month, year) => {
    const user_id = Cookies.get("userId");
    if (!user_id) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      // If month is 0 (All Months), pass month=0 to the API
      const response = await getLLmCost(user_id, month === 0 ? 0 : month, year);
      if (response && response.projects.length === 0) {
        setLlmCostData(null);
        return;
      }

      // Transform response logic remains the same
      const updatedResponse = {
        ...response,
        projects: response.projects.map((project) => {
          let projectTotalCost = 0;

          const updatedAgents = project.agents.map((agent) => {
            let agentTotalCost = 0;

            const updatedCostsByDate = {};
            Object.entries(agent.costs_by_date).forEach(([date, cost]) => {
              const multipliedCost = parseFloat(cost.replace("$", "")) * 10;
              updatedCostsByDate[date] = `$${multipliedCost.toFixed(6)}`;
              agentTotalCost += multipliedCost;
            });

            return {
              ...agent,
              costs_by_date: updatedCostsByDate,
              total_cost: `$${agentTotalCost.toFixed(6)}`,
            };
          });

          projectTotalCost = updatedAgents.reduce(
            (total, agent) =>
              total + parseFloat(agent.total_cost.replace("$", "")),
            0
          );

          return {
            ...project,
            agents: updatedAgents,
            project_cost: `$${projectTotalCost.toFixed(6)}`,
          };
        }),
      };

      const totalUserCost = updatedResponse.projects.reduce(
        (total, project) =>
          total + parseFloat(project.project_cost.replace("$", "")),
        0
      );
      updatedResponse.user_cost = `$${totalUserCost.toFixed(6)}`;

      setLlmCostData(updatedResponse);
      await loadProjectTitles(response.projects);
    } catch (error) {
      
      setLlmCostData(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadLLMCost(selectedMonth, selectedYear);
  }, [selectedMonth, selectedYear]);

  const handleMonthChange = (event) => {
    const newMonth = Number(event.target.value);
    setSelectedMonth(newMonth);
    // Reset current page when changing month
    setCurrentPage(1);
    // If "All Months" is selected, we don't need the year
    if (newMonth === 0) {
      loadLLMCost(0, null);
    } else {
      loadLLMCost(newMonth, selectedYear);
    }
  };
  const handleYearChange = (event) => {
    setSelectedYear(Number(event.target.value));
    setCurrentPage(1); // Reset to first page when changing year
  };

  const handleRowsPerPageChange = (event) => {
    setRowsPerPage(Number(event.target.value));
    setCurrentPage(1); // Reset to first page when changing rows per page
  };

  const toggleRows = (elementId, event) => {
    if (event) {
      event.stopPropagation();
    }

    setExpandedStates((prev) => {
      const newStates = new Set(prev);
      if (newStates.has(elementId)) {
        newStates.delete(elementId);
      } else {
        newStates.add(elementId);
      }
      return newStates;
    });
  };

  const formatAgentName = (name) => {
    return name
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  };
  return (
    <div className="flex flex-col h-[calc(100vh-15vh)]">
      {/* Compact Header Section */}
      <div className="bg-white shadow-sm z-10">
        {/* Stats and Filters - More compact */}
        <div className="px-4 py-3 border-b border-semantic-gray-200">
          <div className="flex justify-between items-center mb-3">
            <div className="flex justify-between w-full">
              <div className="w-1/2">
                <div className="">
                  <h2 className="project-panel-heading">Total Projects</h2>
                  <span className="typography-heading-4">
                    {llmCostData?.projects?.length || 0}
                  </span>
                </div>
              </div>
              <div className="w-1/2 text-right">
                <div>
                  <h2 className="project-panel-heading">Total Cost</h2>
                  <span className="typography-heading-4">
                    {llmCostData?.user_cost || "$0.00"}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div className="flex gap-4">
            <div className="flex gap-2">
              <select
                className="px-3 py-1.5 border rounded-md w-[130px] appearance-none bg-white bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQgNmw0IDQgNC00IiBzdHJva2U9IiM2QjdCOTMiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+PC9zdmc+')]"
                style={{
                  backgroundRepeat: "no-repeat",
                  backgroundPosition: "right 0.5rem center",
                  paddingRight: "2rem",
                }}
                value={selectedMonth}
                onChange={handleMonthChange}
              >
                {months.map((month) => (
                  <option key={month.value} value={month.value}>
                    {month.label}
                  </option>
                ))}
              </select>

              {/* Only show year selector when a specific month is selected */}
              {selectedMonth !== 0 && (
                <select
                  className="px-3 py-1.5 border rounded-md w-[90px] appearance-none bg-white bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQgNmw0IDQgNC00IiBzdHJva2U9IiM2QjdCOTMiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+PC9zdmc+')]"
                  style={{
                    backgroundRepeat: "no-repeat",
                    backgroundPosition: "right 0.5rem center",
                    paddingRight: "2rem",
                  }}
                  value={selectedYear}
                  onChange={handleYearChange}
                >
                  {years.map((year) => (
                    <option key={year} value={year}>
                      {year}
                    </option>
                  ))}
                </select>
              )}
            </div>

            <input
              type="text"
              placeholder="Search by project ID or agent name..."
              className="px-3 py-1.5 border rounded-md flex-1"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>
      </div>

      {/* Scrollable Content Section */}
      <div className="flex-1 overflow-auto custom-scrollbar">
        {loading ? (
          <div className="p-4 text-center ">
            <Loading2 />
          </div>
        ) : (
          <div className="relative">
            <table className="w-full">
              <thead className="bg-semantic-gray-50 sticky top-0 z-10">
                <tr>
                  <th className="px-4 py-3 text-left project-panel-title ">
                    Project Details
                  </th>
                  <th className="px-4 py-3 project-panel-title text-right">
                    Total Amount
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-semantic-gray-200">
                {getPaginatedData(
                  llmCostData?.projects?.filter((project) => {
                    const searchLower = searchTerm.toLowerCase();
                    return (
                      project.project_id.toString().includes(searchLower) ||
                      projectTitles[project.project_id]
                        ?.toLowerCase()
                        .includes(searchLower) ||
                      project.agents.some((agent) =>
                        agent.agent_name.toLowerCase().includes(searchLower)
                      )
                    );
                  })
                )?.map((project, projectIndex) => (
                  <React.Fragment key={project.project_id}>
                    {/* Project Row */}
                    <tr className="hover:bg-semantic-gray-50">
                      <td className="px-4 py-3">
                        <button
                          onClick={(e) =>
                            toggleRows(`project-${projectIndex}`, e)
                          }
                          className="flex items-center gap-2 w-full"
                        >
                          <span className="transform transition-transform duration-200">
                            {expandedStates.has(`project-${projectIndex}`)
                              ? "▼"
                              : "▶"}
                          </span>
                          <div className="flex items-center gap-4">
                            <span className="font-weight-medium">
                              {projectTitles[project.project_id] ||
                                "Loading..."}
                            </span>
                            <span className="project-panel-content text-semantic-gray-500 bg-semantic-gray-100 px-2 py-1 rounded-md">
                              ID: {project.project_id}
                            </span>
                          </div>
                        </button>
                      </td>
                      <td className="px-4 py-3 text-right project-panel-heading">
                        {project.project_cost}
                      </td>
                    </tr>

                    {/* Agent Rows */}
                    {expandedStates.has(`project-${projectIndex}`) &&
                      project.agents.map((agent, agentIndex) => (
                        <React.Fragment
                          key={`${project.project_id}-${agentIndex}`}
                        >
                          <tr className="bg-semantic-gray-50">
                            <td className="px-4 py-3 pl-12">
                              <button
                                onClick={(e) =>
                                  toggleRows(
                                    `agent-${projectIndex}-${agentIndex}`,
                                    e
                                  )
                                }
                                className="flex items-center gap-2 w-full"
                              >
                                <span className="transform transition-transform duration-200">
                                  {expandedStates.has(
                                    `agent-${projectIndex}-${agentIndex}`
                                  )
                                    ? "▼"
                                    : "▶"}
                                </span>
                                <BootstrapTooltip
                                  title={`Agent: ${formatAgentName(
                                    agent.agent_name
                                  )}`}
                                  placement="top"
                                >
                                  <span className="bg-primary-100 text-primary-800 px-3 py-1 rounded-full">
                                    {formatAgentName(agent.agent_name)}
                                  </span>
                                </BootstrapTooltip>
                              </button>
                            </td>
                            <td className="px-4 py-3 text-right project-panel-heading">
                              {agent.total_cost}
                            </td>
                          </tr>

                          {/* Token Details */}
                          {expandedStates.has(
                            `agent-${projectIndex}-${agentIndex}`
                          ) &&
                            Object.entries(agent.tokens_by_date).map(
                              ([date, tokens]) => (
                                <tr key={date} className="bg-semantic-gray-100">
                                  <td className="px-4 py-3 pl-16">
                                    <div className="flex flex-wrap gap-3">
                                      <span className="bg-semantic-gray-200 px-3 py-1 rounded-full project-panel-content">
                                        {date}
                                      </span>
                                      <BootstrapTooltip
                                        title="Total input tokens for this agent"
                                        placement="top"
                                      >
                                        <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full project-panel-content cursor-help">
                                          Input:{" "}
                                          {formatNumber(tokens.input_tokens)}
                                        </span>
                                      </BootstrapTooltip>
                                      <BootstrapTooltip
                                        title="Total output tokens for this agent"
                                        placement="top"
                                      >
                                        <span className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full project-panel-content cursor-help">
                                          Output:{" "}
                                          {formatNumber(tokens.output_tokens)}
                                        </span>
                                      </BootstrapTooltip>
                                    </div>
                                  </td>
                                  <td className="px-4 py-3 text-right project-panel-heading">
                                    {agent.costs_by_date[date]}
                                  </td>
                                </tr>
                              )
                            )}
                        </React.Fragment>
                      ))}
                  </React.Fragment>
                ))}
              </tbody>
            </table>

            {/* No Results Message */}
            {!loading &&
              (!llmCostData?.projects ||
                llmCostData.projects.filter((project) => {
                  const searchLower = searchTerm.toLowerCase();
                  return (
                    project.project_id.toString().includes(searchLower) ||
                    projectTitles[project.project_id]
                      ?.toLowerCase()
                      .includes(searchLower) ||
                    project.agents.some((agent) =>
                      agent.agent_name.toLowerCase().includes(searchLower)
                    )
                  );
                }).length === 0) && (
                <div className="p-4 text-center text-semantic-gray-500">
                  {searchTerm ? (
                    <EmptyStateView type="noLLMSearchResults" />
                  ) : (
                    // : 'No data available for the selected period'}
                    <EmptyStateView type="noLLMCostData" />
                  )}
                </div>
              )}
          </div>
        )}
      </div>

      {/* Fixed Footer with Pagination and Rows Per Page */}
      {llmCostData?.projects && llmCostData.projects.length > 0 && (
        <div className="border-t bg-white py-2 px-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-6">
              <div className="flex items-center gap-2">
                <span className="project-panel-content text-semantic-gray-600">
                  Rows per page:
                </span>
                <select
                  className="px-2 py-1 border rounded-md project-panel-content appearance-none bg-white bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQgNmw0IDQgNC00IiBzdHJva2U9IiM2QjdCOTMiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+PC9zdmc+')]"
                  style={{
                    backgroundRepeat: "no-repeat",
                    backgroundPosition: "right 0.5rem center",
                    paddingRight: "2rem",
                  }}
                  value={rowsPerPage}
                  onChange={handleRowsPerPageChange}
                >
                  {rowsPerPageOptions.map((option) => (
                    <option key={option} value={option}>
                      {option}
                    </option>
                  ))}
                </select>
              </div>
              <div className="project-panel-content text-semantic-gray-700">
                Showing {(currentPage - 1) * rowsPerPage + 1} to{" "}
                {Math.min(
                  currentPage * rowsPerPage,
                  llmCostData.projects.length
                )}{" "}
                of {llmCostData.projects.length} entries
              </div>
            </div>
            <div className="flex gap-2">
              <button
                onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className="px-3 py-1 border rounded-md disabled:opacity-50 hover:bg-semantic-gray-50 project-panel-content"
              >
                Previous
              </button>
              <button
                onClick={() =>
                  setCurrentPage((prev) =>
                    Math.min(prev + 1, getTotalPages(llmCostData.projects))
                  )
                }
                disabled={currentPage === getTotalPages(llmCostData.projects)}
                className="px-3 py-1 border rounded-md disabled:opacity-50 hover:bg-semantic-gray-50 project-panel-content"
              >
                Next
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CostTable;
