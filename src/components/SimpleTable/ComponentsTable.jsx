import React, { useState, useEffect, useContext } from "react";
import { Hash , CircleChevronDown, CircleChevronRight } from "lucide-react";
import Image from "next/image";
import checboxicon from '../../../public/images/checkBox_icon.svg';
import { fetchChildArchitecture, fetchRootArchitecture } from "@/utils/api";
import { ArchitectureContext } from "../Context/ArchitectureContext";;
import { FaEllipsisV } from "react-icons/fa";
import { Loading2 } from "../Loaders/Loading";
import EnParser from '@/utils/enParser';
import en from "../../en.json"

const ComponentTable = ({ projectId, handleItemClick, nodeId, isChild = false }) => {
  const { updateArchitectures, setArchitectureRootId } = useContext(ArchitectureContext);
  const [checkall, setCheckAll] = useState(false);
  const [sortConfig, setSortConfig] = useState({ key: 'id', direction: 'ascending' });
  const [expandedRows, setExpandedRows] = useState([]);
  const [hoveredRow, setHoveredRow] = useState(null);
  const [childLoading, setChildLoading] = useState(null);
  const [childData, setChildData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [loading, setLoading] = useState(true);  // Loading state

  useEffect(() => {
    const fetchData = async () => {
      try {
        let data;
        if (isChild) {
          data = await fetchChildArchitecture(nodeId);
        } else {
          data = await fetchRootArchitecture(projectId);
          if (data.root?.id) {
            setArchitectureRootId(data.root.id);
          }
        }

        if (data.children || isChild) {
          const children = isChild ? data : data.children;
          setChildData(children);
          setFilteredData(children);
          updateArchitectures(children);
        }
      } catch (error) {
        
      } finally {
        setLoading(false);  // Stop loading after data is fetched
      }
    };

    fetchData();
  }, [projectId, nodeId, isChild]);

  const requestSort = (key) => {
    let direction = 'ascending';
    if (sortConfig.key === key && sortConfig.direction === 'ascending') {
      direction = 'descending';
    }
    setSortConfig({ key, direction });
  };

  const handleExpandRow = async (itemId, level) => {
    const isExpanded = expandedRows.some(
      (row) => row.id === itemId && row.level === level
    );
    if (isExpanded) {
      setExpandedRows(
        expandedRows.filter((row) => !(row.id === itemId && row.level === level))
      );
    } else {
      setExpandedRows([...expandedRows, { id: itemId, level }]);
      setChildLoading(itemId);
      try {
        const childArchitecture = await fetchChildArchitecture(itemId);

        const addChildData = (items) => {
          return items.map((item) => {
            if (item.id == itemId) {
              return {
                ...item,
                subItems: childArchitecture.length
                  ? childArchitecture
                  : [{
                      id: null,
                      title: "No child architecture components available",
                      status: "",
                      assignee_name: "",
                      subItems: [],
                    }],
              };
            }
            if (item.subItems) {
              return { ...item, subItems: addChildData(item.subItems) };
            }
            return item;
          });
        };
        setChildData(addChildData(childData));
        setFilteredData(addChildData(childData));
      } catch (error) {
        
      } finally {
        setChildLoading(null);
      }
    }
  };

  const renderRows = (items, level = 0) => {
    return items.map((item) => (
      <React.Fragment key={item.id || `no-child-${level}`}>
        <tr
          className={`cursor-pointer table-val{ relative  ${
            hoveredRow === item.id ? 'bg-semantic-gray-100' : ''
          }`}
          onMouseEnter={() => setHoveredRow(item.id)}
          onMouseLeave={() => setHoveredRow(null)}
        >
          <td className="py-2 px-4 border-b text-center">
            {item.id && <Image src={checboxicon} alt="Check box" />}
          </td>
          <td
            className="py-2 px-4 border-b text-center"
            style={{ paddingLeft: `${level * 40}px` }}
          >
            {item.id !== null && item.has_child ? (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleExpandRow(item.id, level);
                }}
              >
                {expandedRows.some(
                  (row) => row.id === item.id && row.level === level
                ) ? (
                  <CircleChevronDown width={18} height={18} />
                ) : (
                  <CircleChevronRight width={18} height={18} />
                )}
              </button>
            ) : (
              <button disabled>
                <CircleChevronRight width={18} height={18} style={{ opacity: 0.5 }} />
              </button>
            )}
          </td>
          <td className="py-2 px-2 border-b text-center">{item.id}</td>
          {item.id === null ? (
            <td className="py-2 px-4 border-b text-center">
             <EnParser content= {en.ChildArchitectureNotAvailable} />
            </td>
          ) : (
            <>
              <td
                className="py-2 px-4 border-b underline-on-hover"
                style={{ cursor: 'pointer' }}
                onClick={() => handleItemClick(item.id)}
              >
                <div className="truncate">{item.properties?.Title}</div>
              </td>
              
              <td className="py-2 px-4 border-b">
                  <span className="bg-semantic-gray-200 rounded-xl px-2 py-0.5 typography-body-sm">
                    {item.properties?.Type || "N/A"}
                  </span>
                </td>
              <td className="py-2 px-4 border-b ">
                <span className="px-3 py-1 ">
                  {item.properties?.Description.length > 30
                    ? `${item.properties.Description.slice(0, 30)}...`
                    : item.properties?.Description || 'TODO'}
                </span>
                {hoveredRow === item.id && item.has_child && (
                  <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                    <FaEllipsisV
                      onClick={(e) => {
                        e.stopPropagation();
                        handleExpandRow(item.id, level);
                      }}
                    />
                  </div>
                )}
              </td>
            </>
          )}
        </tr>
        {expandedRows.some(
          (row) => row.id === item.id && row.level === level
        ) &&
          childLoading === item.id && (
            <tr key={`loading-${item.id}`}>
              <td colSpan="7" className="py-2 px-4 border-b text-center">
                Loading...
              </td>
            </tr>
          )}
        {expandedRows.some(
          (row) => row.id === item.id && row.level === level
        ) &&
          item.subItems &&
          renderRows(item.subItems, level + 1)}
      </React.Fragment>
    ));
  };

  return (
    <div>
      {loading ? (  // Display loader when loading
        <div className="w-full flex justify-center">
          <Loading2/>
        </div>
      ) : filteredData.length === 0 ? (  // Display message if no data
        <div className="flex justify-center items-center py-10 text-semantic-gray-500">
        <EnParser content= {en.ComponentsNotAvailable} />
        </div>
      ) : (
        <table className="w-full  table-auto">
          <thead className="table-header uppercase h-10 bg-semantic-gray-100">
            <tr>
              <th className="px-4 py-2">
                <input
                  id="default-checkbox"
                  type="checkbox"
                  className="w-4 h-4 text-primary bg-semantic-gray-100 border-semantic-gray-300 rounded focus:ring-primary focus:ring-0"
                  onClick={() => setCheckAll(!checkall)}
                />
              </th>
              <th className="px-6 py-1"></th>
              <th
                className="px-2 py-2 text-center cursor-pointer"
                onClick={() => requestSort("id")}
              >
                <div className="flex">
                  <Hash width={15} height={15} className="mt-1" />
                  <div className="inline-block">
                    <span
                      style={{ marginBottom: "-4px" }}
                      className={`block ${sortConfig.key === "id" && sortConfig.direction === "ascending"
                        ? "text-semantic-gray-800"
                        : "text-semantic-gray-400"
                      }`}
                    >
                      ▲
                    </span>
                    <span
                      className={`block ${sortConfig.key === "id" && sortConfig.direction === "descending"
                        ? "text-semantic-gray-800"
                        : "text-semantic-gray-400"
                      }`}
                    >
                      ▼
                    </span>
                  </div>
                </div>
              </th>
              <th className="table-heading px-6 py-1">NAME</th>
              <th className="table-heading px-6 py-1">TYPE</th>
              <th className="table-heading px-6 py-1">DESCRIPTION</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y">
            {renderRows(filteredData)}
          </tbody>
        </table>
      )}
    </div>
  );
};

export default ComponentTable;
