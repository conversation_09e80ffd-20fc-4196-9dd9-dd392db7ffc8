"use client";
import React, { useState, useEffect, useMemo } from "react";
import { Trash2, RefreshCw, Info, ChevronUp, ChevronDown, X, AlertCircle, Check, Folder } from "lucide-react";
import { useProjectAsset } from "../Context/ProjectAssetContext";
import { repoSynchronization } from "@/utils/gitAPI";
import { useWebSocket } from "../Context/WebsocketContext";
import { usePathname } from "next/navigation";
import { useBuildProgress } from '../Context/BuildProgressContext';
import { createPortal } from "react-dom";
import { useUser } from "../Context/UserContext";
import { BootstrapTooltip } from "../UIComponents/ToolTip/Tooltip-material-ui";

const ProgressStatus = ({ buildId }) => {
  const [showProcessingStatus, setShowProcessingStatus] = useState(false);
  const { buildProgress } = useBuildProgress();
  const progress = buildProgress[buildId] || 0;

  const percentage = progress.percentageComplete || 0;
  const displayStatus = percentage === 100 ? "Success" : "Processing";
  const isProcessing = displayStatus === "Processing";

  return (
    <div className="flex items-center space-x-2 w-48">
      <div className="flex-1">
        <div className="flex justify-between items-center mb-1">
          <span className={`typography-caption font-weight-medium ${isProcessing ? "text-primary" : "text-green-600"}`}>
            {percentage.toFixed(1)}%
          </span>
          {isProcessing && (
            <Info
              className="w-3.5 h-3.5 text-semantic-gray-400 hover:text-semantic-gray-600 cursor-pointer transition-colors"
              onClick={() => setShowProcessingStatus(true)}
              aria-label="View processing details"
            />
          )}
        </div>
        <div className="h-1.5 relative bg-semantic-gray-100 rounded-full overflow-hidden">
          <div
            className={`absolute left-0 top-0 h-full transition-all duration-500 rounded-full ${isProcessing
              ? "bg-gradient-to-r from-primary-200 to-primary-400"
              : "bg-gradient-to-r from-green-200 to-green-400"
              }`}
            style={{ width: `${percentage}%` }}
          />
            <div
              className="absolute top-0 left-0 h-full w-16 animate-shimmer"
              style={{
                background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
                animation: 'shimmer 1.5s infinite'
              }}
            />
        </div>
      </div>
      {showProcessingStatus && createPortal(
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-semantic-gray-900 bg-opacity-50">
          <div className="bg-white rounded-lg p-4 max-w-sm w-full mx-4 shadow-lg">
            <ProcessingStatus
              buildId={buildId}
              onClose={() => setShowProcessingStatus(false)}
            />
          </div>
        </div>,
        document.body
      )}
    </div>
  );
};

// ProcessingStatus component
const ProcessingStatus = ({ buildId, onClose }) => {
  const { buildProgress, buildStatus } = useBuildProgress();
  const processingStatus = buildProgress[buildId];
  const statusData = buildStatus[buildId];

  return (
    <div className="space-y-4">
      <h2 className="typography-heading-4 font-weight-semibold">Processing Status</h2>
      <div className="space-y-4">
        {processingStatus ? (
          <div className="space-y-3">
            <div className="space-y-2">
              <div className="flex justify-between typography-body-sm">
                <span>Progress</span>
                <span>
                  {processingStatus?.percentageComplete?.toFixed(1)}%
                </span>
              </div>
              <div className="w-full bg-semantic-gray-200 rounded-full h-2">
                <div
                  className="h-2 rounded-full bg-primary"
                  style={{
                    width: `${Math.min(processingStatus?.percentageComplete || 0, 100)}%`,
                  }}
                />
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between typography-body-sm">
                <span>Total Files:</span>
                <span>{processingStatus?.totalFiles || 0}</span>
              </div>
              <div className="flex justify-between typography-body-sm">
                <span>Files Ready:</span>
                <span>{processingStatus?.filesReady || 0}</span>
              </div>
              <div className="flex justify-between typography-body-sm">
                <span>Files Processed:</span>
                <span>{processingStatus?.filesProcessed || 0}</span>
              </div>
              <div className="flex justify-between typography-body-sm">
                <span>Estimated Time:</span>
                <span>{processingStatus?.estimatedTime || 0}</span>
              </div>
              <div className="flex justify-between typography-body-sm">
                <span>Id:</span>
                <span>
                  <a
                    href={`https://us5.datadoghq.com/logs?query=task_id%3A${processingStatus?.buildSessionId || 0}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-primary hover:text-primary-800 underline"
                  >
                    <p title='view logs'>{processingStatus?.buildSessionId || 0}</p>
                  </a>
                </span>
              </div>
            </div>
          </div>
        ) : (
          <div>
            <div className="typography-body-sm">
                <p className="mb-2">Status:</p>
                {statusData? (
                  statusData.map((statusItem, index) => (
                    <div className="flex gap-4 mb-2 items-center" key={`statusItem-${index}`}>
                      {statusItem.status == "Processing" ?
                        <div className="w-4 h-4 rounded-full border-4 border-black/10 border-l-primary animate-spin ease-linear" /> :
                        statusItem.status == "Success" ?
                        <div className="rounded-full w-4 h-4 bg-green-500 border-none flex items-center justify-center">
                          <Check className="w-2.5 h-2.5 text-white" />
                        </div> :
                        <div className="rounded-full w-4 h-4 bg-red-500 border-none flex items-center justify-center">
                          <X className="w-2.5 h-2.5 text-white" />
                        </div>
                      }
                      <p>{statusItem.message}</p>
                    </div>
                  ))
                ): (
                  <p>Getting Status...</p>
                )}
              </div>
          </div>
        )}
      </div>
      <div className="flex justify-end">
        <button
          type="button"
          className="bg-semantic-gray-200 px-4 py-2 rounded hover:bg-semantic-gray-300"
          onClick={onClose}
        >
          Close
        </button>
      </div>
    </div>
  );
};

// StatusBadge component
const StatusBadge = ({ status, percentageComplete }) => {
  const statusConfig = {
    Completed: { bgColor: "bg-green-100", textColor: "text-green-700", dotColor: "bg-green-500" },
    "In Progress": { bgColor: "bg-primary-100", textColor: "text-primary-700", dotColor: "bg-primary-500" },
    Failed: { bgColor: "bg-red-100", textColor: "text-red-700", dotColor: "bg-red-500" },
    "Not Started": { bgColor: "bg-semantic-gray-100", textColor: "text-semantic-gray-700", dotColor: "bg-semantic-gray-500" },
    "Needs Rebuild": { bgColor: "bg-amber-100", textColor: "text-amber-700", dotColor: "bg-amber-500" },
    Rebuilding: { bgColor: "bg-primary-100", textColor: "text-primary-700", dotColor: "bg-primary-500" }
  };

  let displayStatus = status;
  if (percentageComplete === 100) displayStatus = "Completed";

  const config = statusConfig[displayStatus] || statusConfig["Not Started"];

  return (
    <span className={`inline-flex items-center px-3 py-1 typography-caption font-weight-medium rounded-full ${config.bgColor} border border-transparent shadow-sm`}>
      <div className={`w-2 h-2 rounded-full mr-1.5 ${config.dotColor}`} />
      <span className={config.textColor}>{displayStatus}</span>
    </span>
  );
};

// RepoTypeBadge component
const RepoTypeBadge = ({ type }) => {
  const typeConfig = {
    public: { bgColor: "bg-purple-100", textColor: "text-purple-700", dotColor: "bg-purple-500" },
    private: { bgColor: "bg-amber-100", textColor: "text-amber-700", dotColor: "bg-amber-500" },
  };

  const config = typeConfig[type.toLowerCase()] || typeConfig.private;

  return (
    <span className={`inline-flex items-center px-3 py-1 typography-caption font-weight-medium rounded-full ${config.bgColor} border border-transparent shadow-sm`}>
      <div className={`w-2 h-2 rounded-full mr-1.5 ${config.dotColor}`} />
      <span className={config.textColor}>{type}</span>
    </span>
  );
};

// SortableHeader component (Added definition)
const SortableHeader = ({ title, field, requestSort, sortConfig, align = "left" }) => (
  <th
    scope="col"
    className={`px-3 py-3 cursor-pointer ${align === "center" ? "text-center" : "text-left"} typography-body-sm font-weight-medium text-semantic-gray-700`}
    onClick={() => requestSort(field)}
    aria-label={`${title} sortable column`}
  >
    <div className={`flex items-center gap-0.5 ${align === "center" ? "justify-center" : ""}`}>
      {title}
      <div className="inline-flex flex-col ml-1">
        <ChevronUp
          className={`h-3 w-3 ${sortConfig.key === field && sortConfig.direction === "ascending"
            ? "text-primary"
            : "text-semantic-gray-400"
            } transition-transform hover:scale-110`}
        />
        <ChevronDown
          className={`h-3 w-3 ${sortConfig.key === field && sortConfig.direction === "descending"
            ? "text-primary"
            : "text-semantic-gray-400"
            } transition-transform hover:scale-110`}
        />
      </div>
    </div>
  </th>
);

// ActionMenu component
// ActionMenu component with fixed alignment
const ActionMenu = ({ projectId, buildId, branchName, repoId, repoService, onRemoveRepository, status, upstream, handleRefresh, changeRepoStatus, connectToSession, updateBuildStatuses }) => {
  const { is_having_permission } = useUser(); // Add this line at the top
  const [isModalOpen, setIsModalOpen] = useState(false);
  const handleRemove = (e) => {
    e.stopPropagation();
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleSync = async (projectId, buildId, status) => {
    let statusCode = 3;
    let changedStatus = "In Progress";
    if(status === "Completed"){
      statusCode = 2;
      changedStatus = "Rebuilding";
    }
    try {
      changeRepoStatus(repoId, branchName, changedStatus);
      const response = await repoSynchronization(projectId, buildId, statusCode);
      if(response.build_session_id){
        updateBuildStatuses([buildId], "upstream");
        connectToSession(response.build_session_id);
      }
    } catch (error) {

    } finally {
      handleRefresh(false);
    }
  };

  const handleDelete = (e) => {
    e.stopPropagation();
    onRemoveRepository(buildId, repoService);
    setIsModalOpen(false);
  };

  return (
    <td className="px-3 py-3">
      <div className="flex items-center justify-center space-x-2">
        {((status === "Needs Rebuild" || upstream) && (status !== "In Progress" && status !== "Rebuilding")) ? (
          <BootstrapTooltip title={!is_having_permission() ? "You don't have permission" : "Rebuild repository"}>
            <span>
              <button
                className="flex items-center justify-center text-primary hover:bg-primary-100 rounded-full p-1.5 relative disabled:text-semantic-gray-400 disabled:hover:bg-transparent"
                onClick={() => handleSync(projectId, buildId, status)}
                disabled={!is_having_permission()}
                aria-label="Rebuild repository"
              >
                <RefreshCw size={20} className="animate-spin-slow" />
                <span className="absolute top-0 right-0 h-2 w-2 bg-red-500 rounded-full animate-ping"></span>
              </button>
            </span>
          </BootstrapTooltip>
        ) : (
          <button
            className="flex items-center justify-center text-semantic-gray-400 rounded-full p-1.5"
            disabled
            aria-label="Rebuild repository"
            title="Rebuild not available"
          >
            <RefreshCw size={20} />
          </button>
        )}
        <BootstrapTooltip title={!is_having_permission() ? "You don't have permission" : "Remove repository"}>
          <span>
            <button
              className="flex items-center justify-center text-red-600 hover:bg-red-50 rounded-full p-1.5 disabled:text-semantic-gray-400 disabled:hover:bg-transparent"
              onClick={handleRemove}
              disabled={!is_having_permission()}
              aria-label="Remove repository"
            >
              <Trash2 size={20} />
            </button>
          </span>
        </BootstrapTooltip>
      </div>
      {isModalOpen && (
        createPortal(
          <div className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto">
            <div
              className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
              onClick={handleCloseModal}
            />
            <div className="relative bg-white rounded-lg shadow-lg w-full max-w-md mx-4 p-4">
              <div className="flex items-center justify-between border-b pb-2">
                <h2 className="typography-body-lg font-weight-semibold text-semantic-gray-900">Delete Repository</h2>
                <button
                  className="text-semantic-gray-400 hover:text-semantic-gray-600 rounded-md p-1 hover:bg-semantic-gray-100 transition-colors"
                  onClick={handleCloseModal}
                  aria-label="Close"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
              <div className="py-4 space-y-2">
                <p className="text-semantic-gray-600 typography-body-sm">
                  Are you sure you want to delete this repository from Kavia?
                </p>
                <div className="flex items-start p-2 bg-red-50 rounded-md text-red-800">
                  <AlertCircle className="w-5 h-5 mr-2 flex-shrink-0" />
                  <p className="typography-body-sm">
                    By deleting this repository, other related data will also be permanently removed
                  </p>
                </div>
              </div>
              <div className="flex justify-end gap-3 pt-2 border-t">
                <button
                  className="px-4 py-2 typography-body-sm bg-semantic-gray-100 text-semantic-gray-700 rounded-md hover:bg-semantic-gray-200 transition-colors"
                  onClick={handleCloseModal}
                  aria-label="Cancel delete"
                >
                  Cancel
                </button>
                <button
                  className="px-4 py-2 typography-body-sm bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                  onClick={handleDelete}
                  aria-label="Confirm delete"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>,
          document.body
        )
      )}
    </td>
  );
};

// Main RepositoryTable component
const RepositoryTable = ({
  repositories,
  selectedRepositories,
  onCheckboxChange,
  onBranchChange,
  onRemoveRepository,
  onSelectAll,
  currentPage,
  totalItems,
  onPageChange,
  onPageSizeChange,
  pageSize: initialPageSize,
  onBuildComplete,
  isLoading = false,
  handleRefresh,
  changeRepoStatus,
  updateBuildStatuses
}) => {
  const { buildProgress } = useBuildProgress();
  const { rawWebSocketMessage } = useProjectAsset();
  const { connectToSession } = useWebSocket();
  const pathname = usePathname();
  const pathParts = pathname.split("/");
  const projectId = pathParts[3];
  const [sortConfig, setSortConfig] = useState({ key: null, direction: "ascending" });
  const defaultPageSize = 5;
  const [localPageSize, setLocalPageSize] = useState(initialPageSize || defaultPageSize);

  useEffect(() => {
    if (initialPageSize && initialPageSize !== localPageSize) {
      setLocalPageSize(initialPageSize);
    }
  }, [initialPageSize]);

  const getBuildID = (repoId, repoService) => {
    const currentRepo = repositories.find((repo) => repo.id === repoId);
    if(repoService == "localFiles"){
      return currentRepo.builds.buildId;
    }
    else {
      const currentBranch = currentRepo?.branches.find(
        (branch) => branch.name === currentRepo?.selectedBranch
      );
      return currentBranch?.buildId;
    }
  };

  const checkAllSelected = () => {
    return repositories.every((repo) => selectedRepositories.includes(repo.id));
  };

  const handleSelectAll = () => {
    const repoIds = repositories.map((repo) => repo.id);
    onSelectAll(repoIds, checkAllSelected());
  };

  const requestSort = (key) => {
    let direction = "ascending";
    if (sortConfig.key === key && sortConfig.direction === "ascending") {
      direction = "descending";
    }
    setSortConfig({ key, direction });
  };

  const sortedRepositories = useMemo(() => {
    if (!sortConfig.key) return repositories;
    return [...repositories].sort((a, b) => {
      let aValue = a[sortConfig.key];
      let bValue = b[sortConfig.key];
      if (sortConfig.key === "selectedBranch") {
        aValue = a.branches?.find((b) => b.name === a.selectedBranch)?.name || "";
        bValue = b.branches?.find((b) => b.name === b.selectedBranch)?.name || "";
      }
      if (aValue < bValue) return sortConfig.direction === "ascending" ? -1 : 1;
      if (aValue > bValue) return sortConfig.direction === "ascending" ? 1 : -1;
      return 0;
    });
  }, [repositories, sortConfig]);

  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * localPageSize;
    return sortedRepositories.slice(startIndex, startIndex + localPageSize);
  }, [sortedRepositories, currentPage, localPageSize]);

  if (isLoading) {
    return (
      <div className="w-full bg-white border border-[#E9EDF5] rounded-lg shadow p-8 flex justify-center items-center">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!repositories || repositories.length === 0) {
    return (
      <div className="w-full bg-white border border-[#E9EDF5] rounded-lg shadow p-8 flex flex-col items-center justify-center text-semantic-gray-500">
        <svg className="w-12 h-12 mb-2 text-semantic-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"></path>
        </svg>
        <span>No repositories added</span>
      </div>
    );
  }

  useEffect(() => {
    paginatedData.forEach((repo) => {
      const buildId = getBuildID(repo.id, repo.service);
      const percentageComplete = buildProgress[buildId]?.percentageComplete || 0;
      const currentBranch = repo.service == "localFiles" ? "localBranch": repo.branches.find((branch) => branch.name === repo.selectedBranch) ;

      if (percentageComplete === 100) {
        onBuildComplete(repo.id, repo.service, currentBranch.name);
      }
    });
  }, [buildProgress]);

  return (
    <>
      <style jsx global>{`
        @keyframes blink {
          0% { opacity: 1; }
          50% { opacity: 0.5; }
          100% { opacity: 1; }
        }
        .animate-blink {
          animation: blink 1.5s infinite;
        }
      `}</style>
      <div className="w-full bg-white border border-semantic-gray-200 rounded-lg shadow-sm overflow-hidden">
        <div className="max-h-[60vh] overflow-auto">
          <div className="overflow-x-auto w-full">
            <table className="w-full border-collapse divide-y divide-semantic-gray-200">
            <thead className="bg-semantic-gray-50 sticky top-0 z-10 shadow-sm">
              <tr>
                <th className="w-1"></th>
                <th className="w-8 px-0">
                  <div className="flex justify-center">
                    <input
                      type="checkbox"
                      checked={checkAllSelected()}
                      onChange={handleSelectAll}
                      className="w-4 h-4 text-primary bg-semantic-gray-100 border-semantic-gray-300 rounded focus:ring-primary focus:ring-0"
                      aria-label="Select all repositories"
                    />
                  </div>
                </th>
                <SortableHeader title="Repository" field="name" requestSort={requestSort} sortConfig={sortConfig} />
                <SortableHeader title="Branch" field="selectedBranch" requestSort={requestSort} sortConfig={sortConfig} />
                <SortableHeader title="Type" field="repoType" requestSort={requestSort} sortConfig={sortConfig} align="center" />
                <SortableHeader title="Status" field="status" requestSort={requestSort} sortConfig={sortConfig} align="center" />
                <th className="px-3 py-3 text-center typography-body-sm font-weight-medium text-semantic-gray-700">Progress</th>
                <th className="px-3 py-3 text-center typography-body-sm font-weight-medium text-semantic-gray-700">Action</th>
              </tr>
            </thead>
              <tbody className="bg-white divide-y divide-semantic-gray-200">
                {paginatedData.map((repo) => {
                  const buildId = getBuildID(repo.id, repo.service);
                  const percentageComplete = buildProgress[buildId]?.percentageComplete || 0;
                  const currentBranch = repo.service == "localFiles"? "" : repo.branches.find((branch) => branch.name === repo.selectedBranch);
                  const hasUpstream = repo.service == "localFiles"? false : currentBranch?.upstream === true;

                  return (
                    <tr
                      key={repo.id}
                      className="hover:bg-semantic-gray-50 transition-colors duration-150 border-b border-semantic-gray-100 last:border-b-0"
                    >
                      <td className="w-1 px-0 py-3"></td>
                      <td className="w-8 px-0 py-3">
                        <div className="flex justify-center">
                          <input
                            type="checkbox"
                            checked={selectedRepositories.includes(repo.id)}
                            onChange={() => onCheckboxChange(repo.id)}
                            className="w-4 h-4 text-primary bg-semantic-gray-100 border-semantic-gray-300 rounded focus:ring-primary focus:ring-0"
                            aria-label={`Select ${repo.name}`}
                          />
                        </div>
                      </td>
                      <td className="px-3 py-3">
                          <div className="flex items-center gap-2">
                          {repo.service == "github" ?
                            <div className="flex-shrink-0 w-6 h-6 flex items-center justify-center rounded-md bg-primary-50 text-primary">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4" />
                                <path d="M9 18c-4.51 2-5-2-7-2" />
                              </svg>
                            </div> :
                            <div className="flex-shrink-0 w-6 h-6 flex items-center justify-center rounded-md bg-primary-50 text-primary">
                              <Folder size={16} className="text-primary" />
                          </div>
                          }
                          <a
                            href={repo.gitUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-primary hover:text-primary-700 truncate max-w-[200px] pr-1 font-weight-medium"
                            title={repo.name}
                            aria-label={`Open ${repo.name} on GitHub`}
                          >
                            {repo.name}
                          </a>
                        </div>
                      </td>
                      <td className="px-3 py-2 text-center">
                        {repo.service !== "localFiles" ? (
                          <select
                            value={repo.selectedBranch}
                            onChange={(e) => onBranchChange(repo.id, e.target.value)}
                            className="block w-full max-w-[150px] pl-3 pr-8 py-1 typography-body-sm border-semantic-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                            aria-label={`Select branch for ${repo.name}`}
                          >
                            {repo.branches.map((branch) => (
                              <option key={branch.name} value={branch.name}>
                                {branch.name}
                              </option>
                            ))}
                          </select>) : <p>-</p>
                        }
                      </td>
                      <td className="px-3 py-3 text-center">
                        <RepoTypeBadge type={repo.repoType} />
                      </td>
                      <td className="px-3 py-3 text-center">
                        <StatusBadge status={percentageComplete === 100 ? 'Completed' : repo.status} percentageComplete={percentageComplete} />
                      </td>
                      <td className="px-3 py-3 text-center">
                        {repo.status === "Completed" || repo.status == "Needs Rebuild" || repo.status == "Failed" || percentageComplete === 100 ? "-" : (
                          <ProgressStatus buildId={buildId} />
                        )}
                      </td>
                      <ActionMenu
                        projectId={projectId}
                        buildId={buildId}
                        branchName={repo.selectedBranch}
                        repoId={repo.id}
                        repoService={repo.service}
                        onRemoveRepository={onRemoveRepository}
                        status={repo.status}
                        upstream={hasUpstream}
                        handleRefresh={handleRefresh}
                        changeRepoStatus={changeRepoStatus}
                        connectToSession={connectToSession}
                        updateBuildStatuses={updateBuildStatuses}
                      />
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
        <div className="px-6 py-4 flex flex-wrap items-center justify-between border-t border-semantic-gray-200 bg-white gap-4">
          <div className="typography-body-sm text-semantic-gray-600 font-weight-medium">
            Showing {(currentPage - 1) * localPageSize + 1} to{" "}
            {Math.min(currentPage * localPageSize, totalItems)} of {totalItems}
          </div>
          <div className="flex items-center space-x-1">
            <button
              onClick={() => onPageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className="px-3 py-1.5 typography-body-sm rounded-md bg-white border border-semantic-gray-300 text-semantic-gray-700 hover:bg-semantic-gray-50 disabled:opacity-50 disabled:bg-semantic-gray-100 transition-colors"
              aria-label="Previous page"
            >
              Previous
            </button>
            <span className="px-3 py-1.5 typography-body-sm bg-primary-50 text-primary-700 rounded-md font-weight-medium">{currentPage}</span>
            <button
              onClick={() => onPageChange(currentPage + 1)}
              disabled={currentPage * localPageSize >= totalItems}
              className="px-3 py-1.5 typography-body-sm rounded-md bg-white border border-semantic-gray-300 text-semantic-gray-700 hover:bg-semantic-gray-50 disabled:opacity-50 disabled:bg-semantic-gray-100 transition-colors"
              aria-label="Next page"
            >
              Next
            </button>
          </div>
          <div className="flex items-center space-x-2">
            <span className="typography-body-sm text-semantic-gray-600">Rows per page</span>
            <select
              value={localPageSize}
              onChange={(e) => {
                const newSize = Number(e.target.value);
                setLocalPageSize(newSize);
                onPageSizeChange(newSize);
                onPageChange(1);
              }}
              className="border-semantic-gray-300 rounded typography-body-sm p-1 pr-6 focus:ring-1 focus:ring-primary focus:border-primary bg-white shadow-sm"
              aria-label="Rows per page"
            >
              <option value={5}>5</option>
              <option value={10}>10</option>
              <option value={20}>20</option>
              <option value={50}>50</option>
              <option value={100}>100</option>
            </select>
          </div>
        </div>
      </div>
    </>
  );
};

export default RepositoryTable;