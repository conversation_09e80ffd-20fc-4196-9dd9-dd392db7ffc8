import React, { useState } from 'react';
import { useRouter } from 'next/navigation';

const WithInterfaceTable = ({ data = [], projectId }) => {
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'ascending' });
  const router = useRouter();

  const handleSort = (key) => {
    let direction = 'ascending';
    if (sortConfig.key === key && sortConfig.direction === 'ascending') {
      direction = 'descending';
    }
    setSortConfig({ key, direction });
  };

  const sortedData = React.useMemo(() => {
    if (!sortConfig.key) return data;

    const sorted = [...data].sort((a, b) => {
      if (a[sortConfig.key] < b[sortConfig.key]) return sortConfig.direction === 'ascending' ? -1 : 1;
      if (a[sortConfig.key] > b[sortConfig.key]) return sortConfig.direction === 'ascending' ? 1 : -1;
      return 0;
    });

    return sorted;
  }, [data, sortConfig]);

  const handleRedirect = (Id,nodeType) => {
    if (nodeType === "interface"){
      router.push(buildProjectUrl(projectId, `architecture/interfaces/${Id}`));
    }else{
    router.push(buildProjectUrl(projectId, `architecture/${nodeType}/${Id}`));
    }
  };

  const renderRows = (data) => {
    return data.map((item, index) => (
      <tr key={index} className="bg-white border-b hover:bg-semantic-gray-50 cursor-pointer" 
      onClick={(e)=>{
        if (item?.relationship_id){

          handleRedirect(item?.relationship_id,"interface")
        }
        
        }}>
        <td className="px-4 py-2">
          {item?.relationship_id}
        </td>
        <td className="px-4 py-2">
          {item.interfaces_with_properties?.Title || 'N/A'}
        </td>
        <td className="px-4 py-2">
          {item.interfaces_with_properties?.Type || 'N/A'}
        </td>
        <td className="px-4 py-2">
          {item.interfaces_with_properties?.Description || 'N/A'}
        </td>
        <td
          className="px-4 py-2  cursor-pointer"
          onClick={(e) => {
            e.stopPropagation();
            handleRedirect(item?.connecting_architecture_id,"high-level");
          }}
        >
          {item?.connecting_architecture_title || 'N/A'} &nbsp;
          {item?.interface_node?.interface_type && <span className="inline-flex items-center px-2.5 py-0.5 rounded-full typography-caption font-weight-bold bg-semantic-gray-200 ">{item.interface_node?.interface_type}</span>}
        </td>
      </tr>
    ));
  };

  return (
    <div className="overflow-x-auto">
      <table className="w-full text-left text-semantic-gray-500 table-auto">
        <thead className="table-header text-semantic-gray-700 uppercase h-10 bg-semantic-gray-100">
          <tr>
          <th
              className="px-4 py-2 cursor-pointer"
              onClick={() => handleSort('interface_node.id')}
            >
              ID
            </th>
            <th
              className="px-4 py-2 cursor-pointer"
              onClick={() => handleSort('interfaces_with_properties.Title')}
            >
              Title
            </th>
            <th
              className="px-4 py-2 cursor-pointer"
              onClick={() => handleSort('interfaces_with_properties.Type')}
            >
              Type
            </th>
            <th
              className="px-4 py-2 cursor-pointer"
              onClick={() => handleSort('interfaces_with_properties.Description')}
            >
              Description
            </th>
            <th className="px-4 py-2">Component</th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y table-val">
          {renderRows(sortedData)}
        </tbody>
      </table>
    </div>
  );
};

export default WithInterfaceTable;
