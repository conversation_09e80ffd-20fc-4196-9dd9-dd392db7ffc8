import React from 'react';

// Component definition for ThreeDotLoader
export const ThreeDotLoader = () => {
  return (
    <div className="flex items-center justify-center py-2">
      <div className="flex space-x-1">
        <div className="h-[5px] w-[5px] bg-orange-600 rounded-full animate-bounce opacity-70"
             style={{ animationDuration: '1.5s', animationDelay: '0ms' }}></div>
        <div className="h-[5px] w-[5px] bg-orange-600 rounded-full animate-bounce opacity-70"
             style={{ animationDuration: '1.5s', animationDelay: '300ms' }}></div>
        <div className="h-[5px] w-[5px] bg-orange-600 rounded-full animate-bounce opacity-70"
             style={{ animationDuration: '1.5s', animationDelay: '600ms' }}></div>
      </div>
    </div>
  );
};

// Rocket Animation Loader for processing deployments
export const RocketLoader = () => {
  return (
    <div className="flex items-center py-1.5 scale-90">
      <div className="relative mr-2.5">
        {/* Multiple flame layers for better effect */}
        <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-2.5 h-5 rounded-b-full bg-gradient-to-t from-orange-600 via-orange-500 to-transparent opacity-60 animate-pulse"></div>
        <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1.5 h-3.5 rounded-b-full bg-gradient-to-t from-yellow-500 via-yellow-400 to-transparent opacity-80 animate-flame"></div>
        <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-2.5 rounded-b-full bg-gradient-to-t from-red-500 via-orange-300 to-transparent opacity-70 animate-flame-delay"></div>

        {/* Simple, clear rocket design */}
        <div className="text-orange-500 transform animate-rocket">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
            <path d="M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z" fill="#F26A1B" />
            <path d="m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z" fill="#F26A1B" />
            <path d="M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0" fill="#F26A1B" />
            <path d="M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5" fill="#F26A1B" />
          </svg>
        </div>

        {/* Small particles/sparks for enhanced effect */}
        <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-4 h-2 flex justify-center">
          <div className="w-0.5 h-0.5 bg-yellow-300 rounded-full animate-spark-1 opacity-80"></div>
          <div className="w-0.5 h-0.5 bg-orange-400 rounded-full animate-spark-2 opacity-80"></div>
          <div className="w-0.5 h-0.5 bg-red-400 rounded-full animate-spark-3 opacity-80"></div>
        </div>
      </div>
    </div>
  );
};

// Rollback Progress Bar for rollback operations
export const RollbackProgressBar = () => {
  return (
    <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50 bg-white shadow-lg rounded-lg border border-semantic-gray-200 px-4 py-3">
      <div className="flex items-center space-x-3">
        <div className="text-orange-500">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="animate-spin">
            <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8" />
            <path d="M3 3v5h5" />
          </svg>
        </div>
        <div className="flex flex-col">
          <span className="text-sm font-medium text-semantic-gray-900">Rolling back to checkpoint...</span>
          <span className="text-xs text-semantic-gray-500">Stashing current changes and restoring previous state</span>
        </div>
      </div>
      <div className="mt-2 w-full bg-semantic-gray-200 rounded-full h-1">
        <div className="bg-orange-500 h-1 rounded-full animate-pulse" style={{ width: '100%' }}></div>
      </div>
    </div>
  );
}; 