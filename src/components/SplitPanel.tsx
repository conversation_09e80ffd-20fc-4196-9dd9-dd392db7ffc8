import React, { useContext, useRef, ReactNode } from "react";
import { StateContext } from "./Context/StateContext";
import { ChevronUp, ChevronDown, ChevronRight, ChevronLeft } from "lucide-react";
import { BootstrapTooltip } from "./UIComponents/ToolTip/Tooltip-material-ui";
import { ModalContext } from "./Context/ModalContext";

interface SplitPanelProps {
    children: [ReactNode, ReactNode, ReactNode];
}

type VerticalPanelState = 'closed' | 'half' | 'full';

interface StateContextType {
    isCollapsed: boolean;
    setIsCollapsed: (value: boolean) => void;
    verticalPanelState: VerticalPanelState;
    setVerticalPanelState: (value: VerticalPanelState | ((prev: VerticalPanelState) => VerticalPanelState)) => void;
}

const useResponsiveLeftPanelWidth = () => {
    const [leftPanelWidth, setLeftPanelWidth] = React.useState(310);

    const debounce = (func: Function, wait: number) => {
        let timeout: NodeJS.Timeout;
        return function executedFunction(...args: any[]) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    };

    React.useEffect(() => {
        const updateWidth = () => {
            setLeftPanelWidth(310);
        };

        updateWidth();
        const debouncedUpdateWidth = debounce(updateWidth, 310);
        window.addEventListener('resize', debouncedUpdateWidth);
        return () => window.removeEventListener('resize', debouncedUpdateWidth);
    }, []);

    return leftPanelWidth;
};

const SplitPanel: React.FC<SplitPanelProps> = ({ children }) => {
    const containerRef = useRef<HTMLDivElement>(null);
    const pane1Ref = useRef<HTMLDivElement>(null);
    const pane2Ref = useRef<HTMLDivElement>(null);
    const { isCollapsed, setIsCollapsed, verticalPanelState, setVerticalPanelState } = useContext(StateContext) as StateContextType;
    const { isModalOpen } = useContext(ModalContext);
    const buttonZIndex = isModalOpen ? 5 : 20;
    const leftPanelWidth = useResponsiveLeftPanelWidth();

    const handleHalfClick = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setVerticalPanelState('half');
    };

    const handleFullClick = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setVerticalPanelState('full');
    };

    const handleToggleDisplay = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setVerticalPanelState((prev) => prev === 'closed' ? 'half' : 'closed');
    };

    return (
        <div ref={containerRef} className="flex h-full w-full relative">
            <div
                ref={pane1Ref}
                className={`panel ${isCollapsed ? "w-0" : "opacity-100"}`}
                style={{
                    width: isCollapsed ? "0" : `${leftPanelWidth}px`,
                    transition: 'width 0.3s ease-in-out'
                }}
            >
                {children[0]}
                <BootstrapTooltip title={isCollapsed ? "Expand" : "Collapse"}>
                    <div
                        className={`absolute top-1/2 transform -translate-y-1/2 ${isCollapsed ? '-right-6' : '-right-3'}`}
                        style={{ zIndex: buttonZIndex }}
                    >
                        <button
                            onClick={() => setIsCollapsed(!isCollapsed)}
                            className="w-6 h-10 bg-white border border-semantic-gray-200 shadow-sm hover:bg-semantic-gray-50 focus:outline-none flex items-center justify-center transition-all duration-200 hover:shadow-md"
                            aria-label={isCollapsed ? "Expand left panel" : "Collapse left panel"}
                        >
                            {isCollapsed ? <ChevronRight className="w-3.5 h-3.5 text-semantic-gray-700" /> : <ChevronLeft className="w-3.5 h-3.5 text-semantic-gray-700" />}
                        </button>
                    </div>
                </BootstrapTooltip>
            </div>

            <div
                className="gutter1 w-[2px]"
                style={{
                    background: isCollapsed ? 'transparent' : 'linear-gradient(to right, rgba(229, 231, 235, 0.5), rgba(229, 231, 235, 0.2))',
                    transition: 'background 0.3s ease-in-out'
                }}
            />

            <div
                ref={pane2Ref}
                className="flex flex-col h-full flex-grow relative"
                style={{
                    width: isCollapsed ? "100%" : `calc(100% - ${leftPanelWidth}px)`,
                    transition: 'width 0.3s ease-in-out, margin-left 0.3s ease-in-out',
                    marginLeft: isCollapsed ? '0' : '5px',
                    paddingRight: '1rem'
                }}
            >
                <div
                    id="pane2-top"
                    className="panel relative w-full bg-white"
                    style={{
                        height: verticalPanelState === 'closed' ? 'calc(100% - 22px)' :
                            verticalPanelState === 'half' ? 'calc(50% - 16px)' : '40px',
                        transition: 'height 0.3s ease',
                        boxShadow: verticalPanelState !== 'closed' ? 'inset 0 -5px 10px -5px rgba(0,0,0,0.03)' : 'none',
                        overflow: verticalPanelState === 'full' ? 'hidden' : 'visible',
                        marginBottom: '22px' // Add space for the execution panel toggle
                    }}
                >
                    <div className="h-full overflow-hidden">
                        {children[1]}
                    </div>
                </div>

                <div
                    id="pane2-bottom"
                    className="z-10 bg-white w-full"
                    style={{
                        height: verticalPanelState === 'closed' ? "0" :
                            verticalPanelState === 'half' ? "calc(50% - 16px)" :
                            "calc(100% - 60px)",
                        display: verticalPanelState === 'closed' ? 'none' : 'flex',
                        flexDirection: 'column',
                        transition: 'height 0.3s ease, box-shadow 0.3s ease',
                        boxShadow: 'inset 0 5px 10px -5px rgba(0,0,0,0.05)',
                        borderTop: '2px solid hsl(var(--semantic-gray-200))',
                        overflow: 'hidden',
                        flex: verticalPanelState === 'full' ? '1 1 auto' : 'initial',
                        position: 'absolute',
                        bottom: '22px',
                        left: 0,
                        right: 0
                    }}
                >
                    <div className="h-full p-1 overflow-auto" style={{ flex: 1 }}>
                        {children[2]}
                    </div>
                </div>

                <div
                    className="h-7 bg-semantic-gray-100 text-semantic-gray-800 flex items-center justify-between px-3 relative flex-shrink-0 border-t border-semantic-gray-200 shadow-sm cursor-pointer w-full"
                    onClick={handleToggleDisplay}
                    style={{
                        marginBottom: 0,
                        position: 'absolute',
                        bottom: 0,
                        left: 0,
                        right: 0,
                        zIndex: 30
                    }}
                >
                    <div className="absolute inset-0 bg-gradient-to-r from-semantic-gray-100 to-semantic-gray-50 opacity-50"></div>
                    <div className="flex items-center gap-1 px-2 py-1 z-10">
                        {verticalPanelState === 'closed' && (
                            <div className="animate-bounce mr-1">
                                <ChevronUp className="w-3 h-3 text-semantic-gray-600" />
                            </div>
                        )}
                        <span className="typography-caption font-weight-medium text-semantic-gray-700 tracking-wide">
                            Execution Panel: {verticalPanelState === 'closed' ? 'Hidden' : verticalPanelState === 'half' ? 'Half View' : 'Full View'}
                        </span>
                    </div>

                    <div className="flex items-center gap-1 z-10">
                        {verticalPanelState !== 'closed' && (
                            <>
                                <button
                                    onClick={handleHalfClick}
                                    className={`flex items-center justify-center px-2 h-5 rounded-sm typography-caption font-weight-medium tracking-wide ${
                                        verticalPanelState === 'half'
                                            ? 'bg-semantic-gray-300 text-semantic-gray-800 shadow-inner'
                                            : 'text-semantic-gray-700 hover:bg-semantic-gray-200 hover:shadow-sm hover:scale-105'
                                    }`}
                                >
                                    Half
                                </button>
                                <button
                                    onClick={handleFullClick}
                                    className={`flex items-center justify-center px-2 h-5 rounded-sm typography-caption font-weight-medium tracking-wide ${
                                        verticalPanelState === 'full'
                                            ? 'bg-semantic-gray-300 text-semantic-gray-800 shadow-inner'
                                            : 'text-semantic-gray-700 hover:bg-semantic-gray-200 hover:shadow-sm hover:scale-105'
                                    }`}
                                >
                                    Full
                                </button>
                            </>
                        )}

                        <button
                            onClick={handleToggleDisplay}
                            className="flex items-center justify-center h-5 px-2 rounded-sm typography-caption font-weight-medium text-semantic-gray-700 hover:bg-semantic-gray-200 hover:shadow-sm hover:scale-105"
                        >
                            {verticalPanelState === 'closed' ? 'Show Execution Display' : 'Hide Execution Display'}
                            {verticalPanelState === 'closed' ? (
                                <ChevronUp className="ml-1 w-3 h-3 text-semantic-gray-600 animate-pulse" />
                            ) : (
                                <ChevronDown className="ml-1 w-3 h-3 text-semantic-gray-600" />
                            )}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default SplitPanel;
