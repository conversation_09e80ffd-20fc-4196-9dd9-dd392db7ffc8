import React from 'react';
import { X, Al<PERSON><PERSON>riangle, <PERSON><PERSON><PERSON>cle, Eye } from 'lucide-react';

const TestCaseDetailModal = ({ testCase, onClose }) => {
  if (!testCase) return null;

  const { properties } = testCase;

  const typeConfig = {
    'FunctionalTestCase': {
      icon: CheckCircle,
      styles: 'bg-primary-100 text-primary-800',
      iconColor: 'text-primary',
      displayText: 'Functional Test Case'
    },
    'NonFunctionalTestCase': {
      icon: Eye,
      styles: 'bg-purple-100 text-purple-800',
      iconColor: 'text-purple-600',
      displayText: 'Non-Functional Test Case'
    },
    'default': {
      icon: AlertTriangle,
      styles: 'bg-semantic-gray-100 text-semantic-gray-600',
      iconColor: 'text-semantic-gray-500',
      displayText: 'Unknown'
    }
  };

  const config = typeConfig[properties.Type || 'default'] || typeConfig['default'];
  const TypeIcon = config.icon || AlertTriangle;

  const formatTextWithLineBreaks = (text) => {
    if (!text) return null;
    // Convert to string if not already a string
    const textStr = typeof text === 'string' ? text : String(text);
    return textStr.split('\n').map((line, i) => (
      <React.Fragment key={i}>
        {line}
        {i < textStr.split('\n').length - 1 && <br />}
      </React.Fragment>
    ));
  };

  // Render different fields based on test case type
  const renderTestCaseSpecificFields = () => {
    if (properties.Type === 'FunctionalTestCase') {
      return (
        <>
          <div className="mb-4">
            <h3 className="typography-body-sm font-weight-semibold text-semantic-gray-700 mb-1">Pre-Conditions</h3>
            <div className="p-3 bg-semantic-gray-50 rounded-md typography-body-sm text-semantic-gray-600 whitespace-pre-wrap">
              {formatTextWithLineBreaks(properties.PreConditions) || 'None specified'}
            </div>
          </div>
          <div className="mb-4">
            <h3 className="typography-body-sm font-weight-semibold text-semantic-gray-700 mb-1">Steps</h3>
            <div className="p-3 bg-semantic-gray-50 rounded-md typography-body-sm text-semantic-gray-600 whitespace-pre-wrap">
              {formatTextWithLineBreaks(properties.Steps) || 'None specified'}
            </div>
          </div>
          <div className="mb-4">
            <h3 className="typography-body-sm font-weight-semibold text-semantic-gray-700 mb-1">Expected Result</h3>
            <div className="p-3 bg-semantic-gray-50 rounded-md typography-body-sm text-semantic-gray-600 whitespace-pre-wrap">
              {formatTextWithLineBreaks(properties.ExpectedResult) || 'None specified'}
            </div>
          </div>
        </>
      );
    } else if (properties.Type === 'NonFunctionalTestCase') {
      return (
        <>
          <div className="mb-4">
            <h3 className="typography-body-sm font-weight-semibold text-semantic-gray-700 mb-1">Test Procedure</h3>
            <div className="p-3 bg-semantic-gray-50 rounded-md typography-body-sm text-semantic-gray-600 whitespace-pre-wrap">
              {formatTextWithLineBreaks(properties.TestProcedure) || 'None specified'}
            </div>
          </div>
          <div className="mb-4">
            <h3 className="typography-body-sm font-weight-semibold text-semantic-gray-700 mb-1">Measurement Metrics</h3>
            <div className="p-3 bg-semantic-gray-50 rounded-md typography-body-sm text-semantic-gray-600 whitespace-pre-wrap">
              {formatTextWithLineBreaks(properties.MeasurementMetrics) || 'None specified'}
            </div>
          </div>
          <div className="mb-4">
            <h3 className="typography-body-sm font-weight-semibold text-semantic-gray-700 mb-1">Expected Results</h3>
            <div className="p-3 bg-semantic-gray-50 rounded-md typography-body-sm text-semantic-gray-600 whitespace-pre-wrap">
              {formatTextWithLineBreaks(properties.ExpectedResults) || 'None specified'}
            </div>
          </div>
          <div className="mb-4">
            <h3 className="typography-body-sm font-weight-semibold text-semantic-gray-700 mb-1">Test Environment</h3>
            <div className="p-3 bg-semantic-gray-50 rounded-md typography-body-sm text-semantic-gray-600 whitespace-pre-wrap">
              {formatTextWithLineBreaks(properties.TestEnvironment) || 'None specified'}
            </div>
          </div>
          <div className="mb-4">
            <h3 className="typography-body-sm font-weight-semibold text-semantic-gray-700 mb-1">Acceptance Criteria</h3>
            <div className="p-3 bg-semantic-gray-50 rounded-md typography-body-sm text-semantic-gray-600 whitespace-pre-wrap">
              {formatTextWithLineBreaks(properties.AcceptanceCriteria) || 'None specified'}
            </div>
          </div>
        </>
      );
    }
    
    return null;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50 overflow-auto p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-3xl max-h-[90vh] overflow-auto">
        <div className="p-4 border-b flex justify-between items-center sticky top-0 bg-white z-10">
          <div className="flex items-center">
            <TypeIcon className={`w-5 h-5 mr-2 ${config.iconColor}`} />
            <h2 className="typography-body-lg font-weight-semibold text-semantic-gray-800">{properties.Title || 'Untitled Test Case'}</h2>
          </div>
          <button 
            onClick={onClose}
            className="text-semantic-gray-400 hover:text-semantic-gray-600 p-1 rounded-full hover:bg-semantic-gray-100"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="p-6">
          <div className="flex flex-wrap gap-3 mb-4">
            <span className={`px-2 py-1 typography-caption font-weight-medium rounded-md ${config.styles}`}>
              {config.displayText}
            </span>
            {properties.Category && (
              <span className="bg-green-100 text-green-800 px-2 py-1 typography-caption font-weight-medium rounded-md">
                {properties.Category}
              </span>
            )}
            {properties.Tags && (
              <div className="flex flex-wrap gap-1">
                {Array.isArray(properties.Tags) 
                  ? properties.Tags.map((tag, index) => (
                      <span key={index} className="bg-semantic-gray-100 text-semantic-gray-700 px-2 py-1 typography-caption font-weight-medium rounded-md">
                        {tag.trim ? tag.trim() : tag}
                      </span>
                    ))
                  : typeof properties.Tags === 'string'
                    ? properties.Tags.split(',').map((tag, index) => (
                        <span key={index} className="bg-semantic-gray-100 text-semantic-gray-700 px-2 py-1 typography-caption font-weight-medium rounded-md">
                          {tag.trim()}
                        </span>
                      ))
                    : <span className="bg-semantic-gray-100 text-semantic-gray-700 px-2 py-1 typography-caption font-weight-medium rounded-md">
                        {String(properties.Tags)}
                      </span>
                }
              </div>
            )}
          </div>

          <div className="mb-6">
            <h3 className="typography-body-sm font-weight-semibold text-semantic-gray-700 mb-1">Description</h3>
            <div className="p-3 bg-semantic-gray-50 rounded-md typography-body-sm text-semantic-gray-600">
              {properties.Description || 'No description available'}
            </div>
          </div>

          {renderTestCaseSpecificFields()}

          <div className="mt-6 pt-4 border-t">
            <div className="flex justify-between items-center">
              <span className="typography-body-sm text-semantic-gray-500">Test Case ID: {testCase.id}</span>
              {testCase.relationship_type && (
                <span className="typography-body-sm text-primary">
                  Relationship: {testCase.relationship_type}
                </span>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestCaseDetailModal; 