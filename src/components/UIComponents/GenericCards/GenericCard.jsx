const GenericCard = ({ title, description, id, type, onClick, disabled, extraInfo }) => {
  return (
    <div
      className={`bg-white rounded-lg border border-semantic-gray-200 p-3 hover:shadow-md transition-all
        ${disabled ? 'cursor-not-allowed opacity-60' : 'cursor-pointer'}`}
      onClick={disabled ? undefined : onClick}
    >
      <div className="space-y-1.5">
        <div>
          <h3 className="typography-body-lg font-weight-semibold text-semantic-gray-900">{title}</h3>
          <p className="typography-body-sm text-semantic-gray-600">{description}</p>
        </div>

        {extraInfo && (
          <div className="mt-1.5">
            {extraInfo}
          </div>
        )}

        <div className="flex items-center justify-between">
          <span className="typography-caption text-semantic-gray-500">{type}</span>
          <span className="typography-body-sm text-purple-600">{id}</span>
        </div>
      </div>
    </div>
  );
};

export default GenericCard;