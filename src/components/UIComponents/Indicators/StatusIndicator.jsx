// components/ui/StatusIndicator.js
import React from 'react';

const StatusIndicator = ({ 
  isLoading = false,
  isActive = false,
  label = '',
  size = 'default',
  className = ''
}) => {
  const sizeClasses = {
    small: {
      container: 'p-2',
      dot: 'w-2 h-2',
      text: 'typography-caption'
    },
    default: {
      container: 'p-4',
      dot: 'w-3 h-3',
      text: 'typography-body-sm'
    },
    large: {
      container: 'p-5',
      dot: 'w-4 h-4',
      text: 'typography-body'
    }
  };

  const selectedSize = sizeClasses[size] || sizeClasses.default;

  if (isLoading) {
    return (
      <div className={`flex items-center rounded-lg bg-semantic-gray-100 animate-pulse ${selectedSize.container} ${className}`}>
        <div className={`${selectedSize.dot} rounded-full mr-2 bg-semantic-gray-300`} />
        <div className={`h-4 bg-semantic-gray-300 rounded w-24 ${selectedSize.text}`}></div>
      </div>
    );
  }

  return (
    <div className={`flex items-center rounded-lg ${
      isActive ? 'bg-green-50' : 'bg-red-50'
    } ${selectedSize.container} ${className}`}>
      <div className={`${selectedSize.dot} rounded-full mr-2 ${
        isActive ? 'bg-green-500' : 'bg-red-500'
      }`} />
      {label && (
        <span className={`font-weight-medium ${
          isActive ? 'text-green-700' : 'text-red-700'
        } ${selectedSize.text}`}>
          {label}
        </span>
      )}
    </div>
  );
};

export default StatusIndicator;