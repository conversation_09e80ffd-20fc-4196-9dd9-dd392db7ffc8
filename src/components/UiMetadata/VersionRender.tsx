import React, { useState } from "react";
import { useRouter, useParams } from "next/navigation";
import dynamic from "next/dynamic";
import { formatUTCToLocal } from "@/utils/datetime";
import { Accordion } from "@/components/UIComponents/Accordions/Accordion";
import { renderHTML } from "@/utils/helpers";
import { Edit2 } from "lucide-react";
import APIDocumentation from "../API/API";
import { BootstrapTooltip } from "../UIComponents/ToolTip/Tooltip-material-ui";
import { diffWords } from "diff";

// Import MarkdownEditor component without type declarations
const MarkdownEditor = dynamic(() => import("../Editor/MarkdownEditor"), {
  ssr: false,
});

// Dynamic import for Mermaid chart
const NoSSR = dynamic(() => import("../Chart/MermaidChart"), { ssr: false });

// Define interfaces
interface Metadata {
  [key: string]: {
    display_type?: string;
    hidden?: boolean;
  };
}

interface Properties {
  [key: string]: any;
  IsArchitecturalLeaf?: string;
}

interface EditField {
  key: string;
  value: string;
  label: string;
}

interface VersionRenderProps {
  properties: Properties;
  metadata: Metadata;
  to_skip?: string[];
  to_show?: string[];
  projectId?: string;
  architectureId?: string;
  onUpdate?: (key: string, value: string) => Promise<void>;
  compareWithProperties?: Properties;
  isPreviousVersion?: boolean; 
}

const VersionRender: React.FC<VersionRenderProps> = ({
  properties,
  metadata,
  to_skip = [],
  to_show = [],
  projectId = "",
  architectureId = "",
  onUpdate,
  compareWithProperties = null,
  isPreviousVersion = false,
}) => {
  const router = useRouter();
  const params = useParams();

  const [isEditorOpen, setIsEditorOpen] = useState<boolean>(false);
  const [currentEditField, setCurrentEditField] = useState<EditField>({
    key: "",
    value: "",
    label: "",
  });

  const handleEdit = (key: string, value: string, label: string): void => {
    setCurrentEditField({ key, value, label });
    setIsEditorOpen(true);
  };

  const handleEditorClose = (): void => {
    setIsEditorOpen(false);
    setCurrentEditField({ key: "", value: "", label: "" });
  };

  const transformKey = (key: string): string => {
    const transformed = key
      .replace(/_/g, " ")
      .replace(/([a-z])([A-Z])/g, "$1 $2")
      .replace(/\b\w/g, (char) => char.toUpperCase());
    return transformed;
  };

  const shouldUseAccordion = (content: string): boolean => {
    if (!content || typeof content !== "string") return false;
    const lineCount = content?.split("\n").length;
    const wordCount = content?.split(/\s+/).length;
    return lineCount > 10 || wordCount > 200;
  };

  const hasValueChanged = (key: string, value: any): boolean => {
    if (!compareWithProperties) return false;
    
    // Check if the key exists in both property sets
    if (!(key in compareWithProperties)) return true; // New field
    
    const compareValue = compareWithProperties[key];
    
    // Handle different data types
    if (typeof value !== typeof compareValue) return true;
    
    // For string values (most common case)
    if (typeof value === 'string' && typeof compareValue === 'string') {
      return value !== compareValue;
    }
    
    // For arrays, compare string representations
    if (Array.isArray(value) && Array.isArray(compareValue)) {
      return JSON.stringify(value) !== JSON.stringify(compareValue);
    }
    
    // For objects, compare string representations
    if (typeof value === 'object' && value !== null && 
        typeof compareValue === 'object' && compareValue !== null) {
      return JSON.stringify(value) !== JSON.stringify(compareValue);
    }
    
    // Default comparison
    return value !== compareValue;
  };

  
  const highlightDifferences = (currentText: any, previousText: any, isPreviousVersion: boolean) => {
    if (!previousText || !currentText || typeof currentText !== 'string' || typeof previousText !== 'string') {
      return isPreviousVersion ? previousText : currentText;
    }
  
    // Determine which text to display and which to compare against based on the view
    const textToDisplay = isPreviousVersion ? previousText : currentText;
    const textToCompare = isPreviousVersion ? currentText : previousText;
    
    // Split both texts into lines
    const displayLines = textToDisplay.split('\n');
    const compareLines = textToCompare.split('\n');
    
    // Process each line separately to maintain line structure
    const processedLines = displayLines.map((displayLine, lineIndex) => {
      // Handle list items specially
      const listItemMatch = displayLine.match(/^(\s*)(\d+\.|\-|\*)\s+(.*)/);
      
      if (listItemMatch) {
        // This is a list item line
        const [, indent, marker, content] = listItemMatch;
        
        // Find corresponding line in comparison text
        const compareLineIndex = compareLines.findIndex(line => 
          line.match(new RegExp(`^\\s*${marker.replace('.', '\\.')}\\s+`))
        );
  
        if (compareLineIndex !== -1) {
          // Found a corresponding list item
          const compareListItemMatch = compareLines[compareLineIndex].match(/^(\s*)(\d+\.|\-|\*)\s+(.*)/);
          if (compareListItemMatch) {
            const compareContent = compareListItemMatch[3];
            
            // Do a word-level diff
            const contentDiff = diffWords(
              isPreviousVersion ? content : compareContent, 
              isPreviousVersion ? compareContent : content
            );
            
            let highlightedContent = '';
            
            contentDiff.forEach(part => {
              if (isPreviousVersion) {
                // In previous version view:
                // - highlight removed content (in old but not in new) in red
                if (part.removed) {
                  highlightedContent += `<span class="bg-red-100 text-red-800">${part.value}</span>`;
                } else if (!part.added) {
                  highlightedContent += part.value;
                }
              } else {
                // In current version view:
                // - highlight added content (in new but not in old) in yellow
                if (part.added) {
                  highlightedContent += `<span class="bg-green-100 text-green-800">${part.value}</span>`;
                } else if (!part.removed) {
                  highlightedContent += part.value;
                }
              }
            });
            
            return `${indent}${marker} ${highlightedContent}`;
          }
        }
        
        // No match found - entire item is either new or removed
        if (isPreviousVersion) {
          // In previous version, if item not in new version, highlight in red
          return `${indent}${marker} <span class="bg-red-100 text-red-800">${content}</span>`;
        } else {
          // In current version, if item not in old version, highlight in yellow
          return `${indent}${marker} <span class="bg-green-100 text-green-800">${content}</span>`;
        }
      } else {
        // Regular text line (not a list item)
        // Find the closest matching line in comparison text
        let bestMatchIndex = -1;
        let bestMatchScore = 0;
        
        compareLines.forEach((compareLine, idx) => {
          // Simple similarity score - proportion of words that match
          const displayWords = displayLine.split(/\s+/);
          const compareWords = compareLine.split(/\s+/);
          let matchingWords = 0;
          
          displayWords.forEach(word => {
            if (compareWords.includes(word)) matchingWords++;
          });
          
          const score = displayWords.length > 0 ? matchingWords / displayWords.length : 0;
          if (score > bestMatchScore) {
            bestMatchScore = score;
            bestMatchIndex = idx;
          }
        });
        
        // If we found a reasonably similar line, do word-level diff
        if (bestMatchScore > 0.3 && bestMatchIndex !== -1) {
          const compareLine = compareLines[bestMatchIndex];
          const lineDiff = diffWords(
            isPreviousVersion ? displayLine : compareLine, 
            isPreviousVersion ? compareLine : displayLine
          );
          
          let lineHtml = '';
          lineDiff.forEach(part => {
            if (isPreviousVersion) {
              // For previous version view
              if (part.removed) {
                lineHtml += `<span class="bg-red-100 text-red-800">${part.value}</span>`;
              } else if (!part.added) {
                lineHtml += part.value;
              }
            } else {
              // For current version view
              if (part.added) {
                lineHtml += `<span class="bg-green-100 text-green-800">${part.value}</span>`;
              } else if (!part.removed) {
                lineHtml += part.value;
              }
            }
          });
          
          return lineHtml;
        } else {
          // No good match found - entire line is either new or removed
          if (isPreviousVersion) {
            // In previous version, if line not in new version, highlight in red
            return `<span class="bg-red-100 text-red-800">${displayLine}</span>`;
          } else {
            // In current version, if line not in old version, highlight in yellow
            return `<span class="bg-green-100 text-green-800">${displayLine}</span>`;
          }
        }
      }
    });
    
    return processedLines.join('\n');
  };

  // Removed formatTechStack - let renderHTML handle all markdown formatting consistently

  const renderMarkdownContent = (content: string, isChanged: boolean = false, key: string = "") => {
    if (typeof content !== "string") return content;

    // Simplified processing - let the main renderHTML function handle markdown formatting
    let processedContent = content;

    // If content has changed and we need to highlight differences
    if (isChanged && compareWithProperties && key) {
      const oldContent = compareWithProperties[key];
      if (oldContent && typeof oldContent === 'string') {
        // Simplified diff highlighting - let renderHTML handle markdown formatting
        if (isPreviousVersion) {
          return highlightDifferences(processedContent, oldContent, true);
        } else {
          return highlightDifferences(processedContent, oldContent, false);
        }
      }
    }

    // If no highlighting needed, use renderHTML for proper markdown formatting
    return renderHTML(processedContent);
  };

  const renderContent = (
    content: string,
    isTechStack: boolean,
    renderToHtml: boolean,
    isChanged: boolean = false,
    key: string = ""
  ): JSX.Element => {
    if (!content) return <></>;
    
    // Removed isTechStack special handling - let renderHTML handle all markdown formatting
    
    if (renderToHtml) {
      if (isChanged && compareWithProperties && key && compareWithProperties[key]) {
        const oldContent = compareWithProperties[key];
        // Make sure we're passing the isPreviousVersion flag consistently
        const processedContent = highlightDifferences(
          content, 
          oldContent, 
          isPreviousVersion
        );
        return <div dangerouslySetInnerHTML={{ __html: renderHTML(processedContent) }} />;
      } else {
        return <div dangerouslySetInnerHTML={{ __html: renderHTML(content) }} />;
      }
    }
    
    // Use different background colors based on previous/current version
    const bgClass = isPreviousVersion && isChanged ? "bg-red-100 p-1 rounded" : 
                   isChanged ? "bg-yellow-100 p-1 rounded" : "";
    
    return <div className={bgClass}>{content}</div>;
  };

  const renderField = (key: string, value: any): JSX.Element | null => {
    if (!key || !metadata[key]) return null;

    const label = transformKey(key);
    const isValueChanged = hasValueChanged(key, value);

    if (metadata[key]?.hidden === true || to_skip.includes(key)) {
      return null;
    }

    const displayType = metadata[key]?.display_type || "text";
    const isTechStack =
      label === "Tech Stack Choices" || label === "Recommended Tech Stack";
    
    // Use different colors for change indicators based on previous/current version
    const changedIndicatorClass = isPreviousVersion 
      ? "ml-2 px-2 py-1 typography-caption bg-red-100 text-red-800 rounded-full"
      : "ml-2 px-2 py-1 typography-caption bg-yellow-100 text-yellow-800 rounded-full";
    
    const changedIndicator = isValueChanged ? (
      <span className={changedIndicatorClass}>
        {isPreviousVersion ? "Removed" : "Changed"}
      </span>
    ) : null;

    switch (label) {
      case "Title":
        return (
          <div key={key} className="bg-white rounded-lg p-4 mb-4">
            <span className="typography-body-lg font-weight-semibold text-[hsl(var(--semantic-gray-700))] flex items-center">
              {isValueChanged && compareWithProperties?.[key] ? (
                <div dangerouslySetInnerHTML={{ 
                  __html: highlightDifferences(value, compareWithProperties[key], isPreviousVersion) 
                }} />
              ) : (
                value
              )}
              {changedIndicator}
            </span>
          </div>
        );

      case "Type":
        return (
          <div key={key} className="bg-white rounded-lg p-4 mb-4">
            <span className="typography-body-sm font-weight-medium px-3 py-1 bg-semantic-gray-100 rounded-full text-semantic-gray-700 flex items-center">
              {isValueChanged && compareWithProperties?.[key] ? (
                <div dangerouslySetInnerHTML={{ 
                  __html: highlightDifferences(value, compareWithProperties[key], isPreviousVersion) 
                }} />
              ) : (
                value
              )}
              {changedIndicator}
            </span>
          </div>
        );

      case "Story Points":
      case "User Story Type":
      case "Assigned To":
        return (
          <div className="bg-white rounded-lg p-4 mb-4" key={key}>
            <div className="flex items-center justify-between mb-2">
              <span className="font-weight-semibold text-[hsl(var(--semantic-gray-700))] flex items-center">{label} {changedIndicator}</span>
              {onUpdate && (
                <BootstrapTooltip title="Edit" placement="top">
                <button
                  onClick={() => handleEdit(key, value, label)}
                  className="flex items-center gap-2 px-3 py-1.5 typography-body-sm text-red-600 bg-white hover:bg-red-50 border border-semantic-gray-200 hover:border-red-300 rounded-md transition-colors duration-200"
                >
                  <Edit2 size={14} className="text-red-600" />
                  <span>Edit</span>
                </button>
                </BootstrapTooltip>
              )}
            </div>
            <div>
              {isValueChanged && compareWithProperties?.[key] ? (
                <div dangerouslySetInnerHTML={{ 
                  __html: highlightDifferences(value, compareWithProperties[key], isPreviousVersion) 
                }} />
              ) : (
                <div className="text-[#464F60]">{value}</div>
              )}
            </div>
          </div>
        );

      case "Due Date":
        return (
          <div className="bg-white rounded-lg p-4 mb-4" key={key}>
            <strong className="font-weight-semibold text-[hsl(var(--semantic-gray-700))] block mb-2">
              {label}
              {changedIndicator}
            </strong>
            <p>
              {isValueChanged && compareWithProperties?.[key] ? (
                <div dangerouslySetInnerHTML={{ 
                  __html: highlightDifferences(
                    formatUTCToLocal(value) || "N/A", 
                    formatUTCToLocal(compareWithProperties[key]) || "N/A",
                    isPreviousVersion
                  ) 
                }} />
              ) : (
                <span className="text-[#464F60]">{formatUTCToLocal(value) || "N/A"}</span>
              )}
            </p>
          </div>
        );

      case "Is Architectural Leaf":
        return <></>; // Hidden as per original code

      default:
        switch (displayType) {
          case "rich_text":
          case "text":
          case "select":
          case "array":
          case "number":
            return (
              <div className="bg-white rounded-lg mb-2" key={key}>
                <Accordion
                  title={`${label} ${isValueChanged ? isPreviousVersion ? '(Removed)' : '(Updated)' : ''}`}
                  defaultOpen={true}
                  preview={`Open this for ${key.toLowerCase()}`}
                  type={displayType}
                  onEdit={
                    onUpdate ? () => handleEdit(key, value, label) : undefined
                  }
                  mode={shouldUseAccordion(value) ? "dynamic" : "static"}
                >
                  <div className="text-[#464F60]">
                    {renderContent(value, isTechStack, true, isValueChanged, key)}
                  </div>
                </Accordion>
              </div>
            );

          case "mermaid_chart":
          case "mermaid":
          case "plantuml":
            return (
              <div className="bg-white rounded-lg mb-2" key={key}>
                <Accordion 
                  title={`${label} ${isValueChanged ? isPreviousVersion ? '(Removed)' : '(Updated)' : ''}`} 
                  defaultOpen={true}
                >
                  {value ? (
                    <NoSSR chartDefinition={value} />
                  ) : (
                    <p className="text-red-500">
                      Error: No chart definition available.
                    </p>
                  )}
                </Accordion>
              </div>
            );

          case "api_doc":
            return (
              <div className="bg-white rounded-lg mb-2" key={key}>
                <Accordion 
                  title={`${label} ${isValueChanged ? isPreviousVersion ? '(Removed)' : '(Updated)' : ''}`} 
                  defaultOpen={true}
                >
                  <APIDocumentation apiDetails={properties} />
                </Accordion>
              </div>
            );

          default:
            if (shouldUseAccordion(value)) {
              return (
                <div className="bg-white rounded-lg mb-2" key={key}>
                  <Accordion
                    title={`${label} ${isValueChanged ? isPreviousVersion ? '(Removed)' : '(Updated)' : ''}`}
                    defaultOpen={true}
                    preview={`Open this for ${key.toLowerCase()}`}
                    type={displayType}
                    onEdit={
                      onUpdate ? () => handleEdit(key, value, label) : undefined
                    }
                  >
                    <div className="text-[#464F60]">
                      {renderContent(value, isTechStack, true, isValueChanged, key)}
                    </div>
                  </Accordion>
                </div>
              );
            }
            return (
              <div className="bg-white rounded-lg p-4 mb-4" key={key}>
                <div className="flex items-center justify-between mb-2">
                  <span className="font-weight-semibold text-[hsl(var(--semantic-gray-700))] flex items-center">{label} {changedIndicator}</span>
                  {onUpdate && (
                    <button
                      onClick={() => handleEdit(key, value, label)}
                      className="flex items-center gap-2 px-3 py-1 typography-body-sm text-semantic-gray-600 hover:text-red-600 bg-white hover:bg-red-50 border  border-red-300 rounded-md transition-colors duration-200"
                    >
                      <Edit2 size={14} className="text-red-600" />
                      <span>Edit</span>
                    </button>
                  )}
                </div>
                <div className="text-[#464F60]">
                  {renderContent(value, isTechStack, true, isValueChanged, key)}
                </div>
              </div>
            );
        }
    }
  };

  const renderProperties = (properties: Properties, metadata: Metadata) => {
    const keysToRender = to_show.length > 0 ? to_show : Object.keys(metadata);
    return (
      <div className="space-y-4">
        {keysToRender.map((key) =>
          properties[key] !== undefined
            ? renderField(key, properties[key])
            : null
        )}
      </div>
    );
  };

  return (
    <div>
      {renderProperties(properties, metadata)}
      {isEditorOpen && (
        <MarkdownEditor
          isOpen={isEditorOpen}
          onClose={handleEditorClose}
          content={currentEditField.value || ""}
          onSave={async (newContent: string) => {
            if (onUpdate) {
              await onUpdate(currentEditField.key, newContent);
            }
            handleEditorClose();
          }}
          title={`Edit ${currentEditField.label}`}
        />
      )}
    </div>
  );
};

export default VersionRender;