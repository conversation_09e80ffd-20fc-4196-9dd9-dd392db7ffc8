// src/components/layout/Header.tsx
"use client";
import Image from "next/image";
import { Bell, Building2 } from "lucide-react";
import { useState, useEffect } from "react";
import logo from "@/../public/logo/kavia_light_logo.svg";
import { getOrganizationNameById } from "@/utils/api";

export const Header = () => {
  const [username, setUsername] = useState("User");
  const [tenantId, setTenantId] = useState("");
  const [orgName, setOrgName] = useState("");

  useEffect(() => {
    // Get username and tenant_id from cookies when component mounts
    const cookieUsername = document.cookie
      .split("; ")
      .find((row) => row.startsWith("username="))
      ?.split("=")[1];

    const cookieTenantId = document.cookie
      .split("; ")
      .find((row) => row.startsWith("tenant_id="))
      ?.split("=")[1];

    if (cookieUsername) {
      setUsername(decodeURIComponent(cookieUsername));
    }
    if (cookieTenantId) {
      const decodedTenantId = decodeURIComponent(cookieTenantId);
      setTenantId(decodedTenantId);
      
      // Fetch organization name when tenant ID is available
      getOrganizationNameById(decodedTenantId).then((response) => {
        setOrgName(response.name);
      });
    }
  }, []);

  return (
    <header className="h-12 bg-white border-b flex items-center justify-between px-3">
      <div className="flex items-center space-x-2">
        <Image
          src={logo}
          alt="Kavia Logo"
          width={26}
          height={26}
          className="ml-2"
        />
        <h2 className="typography-body-lg font-weight-semibold">Kavia</h2>
      </div>

      <div className="flex items-center space-x-3">
        {orgName && (
          <div className="flex items-center">
            <Building2 className="w-6 h-6 text-semantic-gray-600" />
            <span className="typography-heading-2 font-weight-semibold gradient-text drop-shadow-sm ml-2">
              {orgName}
            </span>
          </div>
        )}
        <button className="p-2 hover:bg-semantic-gray-100 rounded-full">
          <Bell className="w-5 h-5 text-semantic-gray-600" />
        </button>
        <div className="flex items-center space-x-2">
          <Image
            className="w-8 h-8 rounded-full"
            src={`https://ui-avatars.com/api/?name=${username}&background=1C64F2&color=FFFFFF`}
            alt="User Avatar"
            width={26}
            height={26}
          />
        </div>
      </div>
    </header>
  );
};
