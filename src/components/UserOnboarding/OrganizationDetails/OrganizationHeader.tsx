// src/components/UserOnboarding/OrganizationDetails/OrganizationHeader.tsx
import { FC, useState } from 'react';
import { DynamicButton } from '@/components/UIComponents/Buttons/DynamicButton';
import { Pencil } from 'lucide-react';
interface OrganizationHeaderProps {
  name: string;
  status: 'active' | 'inactive';
  planType: string;
  onboardedDate: string;
}

const OrganizationHeader: FC<OrganizationHeaderProps> = ({
  name,
  status,
  planType,
  onboardedDate,
}) => {
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  const handleSave = async (data: {
    name: string;
    email: string;
    isSuspended: boolean;
  }) => {
    try {
      // Implement your save logic here
      setIsEditModalOpen(false);
    } catch (error) {
      
    }
  };

  return (
    <>
      <div className="flex items-center justify-between mb-1 mt-1">
        <div className="flex flex-col gap-2">
          <h1 className="typography-heading-2 font-weight-semibold">{name}</h1>
          <div className="flex items-center gap-4">
            <span className={`px-3 py-1 typography-body-sm rounded-full ${
              status === 'active' ? 'bg-green-100 text-green-800' : 'bg-semantic-gray-100 text-semantic-gray-800'
            }`}>
              {status}
            </span>
            <span className="text-semantic-gray-600">{planType}</span>
            <span className="text-semantic-gray-600">Onboarded: {onboardedDate}</span>
          </div>
        </div>
        <DynamicButton
          variant="secondary"
          icon={Pencil}
          text="Edit Organization"
          disable={true}
          // onClick={() => setIsEditModalOpen(true)}
        />
      </div>

      {/* <EditOrganization
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        initialData={{
          name: name,
          email: '<EMAIL>', // You can pass this as prop if needed
          isSuspended: status === 'inactive'
        }}
        onSave={handleSave}
      /> */}
    </>
  );
};

export default OrganizationHeader;