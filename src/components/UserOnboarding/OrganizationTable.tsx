"use client";

import React, { useMemo, useState, useC<PERSON>back, useContext, useEffect } from "react";
import TableComponent from "@/components/SimpleTable/table";
import Search from "@/components/BrowsePanel/TableComponents/Search";
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import { Plus, Filter, Trash, Power, MoreVertical, X } from "lucide-react";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import Loader from "@/components/UserOnboarding/ui/Loader"
import { useRouter } from "next/navigation";
import { deleteOrganization, updateOrganizationStatus } from "@/utils/api";
import DeleteConfirmationModal from "@/components/Modal/DeleteConfirmationModal";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";

interface Organization {
  id: string;
  orgName: string;
  domain: string;
  admin: string;
  email: string;
  status: string;
  cost: string;
  currentCost :string;
  credits: string | Number;
  remainingCredits: string | Number;
  onboardingDate: string;
}

interface OrganizationTableProps {
  data: Organization[];
  isLoading: boolean;
  error?: Error;
  isB2C?: boolean;
  isOpenToPublic?: boolean;
  onToggleOpenToPublic?: () => void;
}

interface FilterState {
  status: string;
}

// NoData Component
const NoData: React.FC<{ message: string }> = ({ message }) => (
  <div className="flex flex-col items-center justify-center py-12">
    <p className="text-semantic-gray-500 typography-body-lg">{message}</p>
  </div>
);

export const OrganizationTable: React.FC<OrganizationTableProps> = ({
  data,
  isLoading,
  error,
  isB2C,
  isOpenToPublic,
  onToggleOpenToPublic,
}) => {
  const router = useRouter();
  // States
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [filters, setFilters] = useState<FilterState>({
    status: "",
  });
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const { showAlert } = useContext(AlertContext);
  const [deleteModalState, setDeleteModalState] = useState<{
    isOpen: boolean;
    orgId: string | null;
    orgName: string | null;
    isLoading: boolean;
  }>({
    isOpen: false,
    orgId: null,
    orgName: null,
    isLoading: false
  });
  const [actionModalState, setActionModalState] = useState<{
    isOpen: boolean;
    orgId: string | null;
    orgName: string | null;
    orgStatus: string | null;
    position?: { top: number; left: number };
    isStatusLoading?: boolean;
  }>({
    isOpen: false,
    orgId: null,
    orgName: null,
    orgStatus: null,
    isStatusLoading: false
  });
  const [toggleConfirmModalState, setToggleConfirmModalState] = useState<{
    isOpen: boolean;
    currentState: boolean;
  }>({
    isOpen: false,
    currentState: false
  });
  const [organizations, setOrganizations] = useState<Organization[]>(data);

  useEffect(() => {
    setOrganizations(data);
  }, [data])

  const routeToCost = (e: React.MouseEvent<HTMLAnchorElement>,orgId: string) => {
    e.stopPropagation();
    router.push(`/dashboard/organizations/${orgId}/costs`)
  }

  // Handlers
  const handleSuspend = useCallback(async (id: string) => {
    try {
      // API call to suspend organization

    } catch (error) {

    }
  }, []);

  const handleDelete = useCallback(async (id: string) => {
    try {
      setDeleteModalState(prev => ({ ...prev, isLoading: true }));
      const response = await deleteOrganization(id);

      if (!response.ok) {
        const errorData = await response.json();
        showAlert(errorData.detail, "danger");
        throw new Error(errorData.detail);
      }

      const updatedData = organizations.map(org => {
        if (org.id === id) {
          return { ...org, status: 'deleted' };
        }
        return org;
      });
      setOrganizations(updatedData);

      setDeleteModalState({ isOpen: false, orgId: null, orgName: null, isLoading: false });
      showAlert("Organization deleted successfully!", "success");
    } catch (error) {

      setDeleteModalState(prev => ({ ...prev, isLoading: false }));
    }
  }, [showAlert, organizations]);

  const handleSearch = (term: string) => {
    setSearchTerm(term);
    setCurrentPage(1); // Reset to first page on search
  };


  const handleRowClick = (id: string) => {
    // Find the organization to check its status
    const organization = organizations.find(org => org.id === id);
    if (organization?.status === 'deleted') {
      return; // Don't navigate for deleted organizations
    }
    router.push(`/dashboard/organizations/${id}/overview`);
  };

  const handleFilterChange = (newFilters: FilterState) => {
    setFilters(newFilters);
    setCurrentPage(1); // Reset to first page on filter change
  };

  const handleFilterClick = () => {
    setIsFilterOpen(!isFilterOpen);
  };

  const handleStatusToggle = useCallback(async (id: string, currentStatus: string) => {
    try {
      // Set loading state
      setActionModalState(prev => ({
        ...prev,
        isStatusLoading: true
      }));

      const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
      const response = await updateOrganizationStatus(id, newStatus);

      // Update the organizations state immediately
      setOrganizations(prevOrgs => prevOrgs.map(org => {
        if (org.id === id) {
          return { ...org, status: newStatus };
        }
        return org;
      }));

      // Update the action modal state to reflect the new status
      setActionModalState(prev => ({
        ...prev,
        orgStatus: newStatus,
        isStatusLoading: false
      }));

      showAlert(`Organization status updated to ${newStatus}`, "success");
    } catch (error) {

      showAlert('Failed to update organization status', "danger");
      // Reset loading state on error
      setActionModalState(prev => ({
        ...prev,
        isStatusLoading: false
      }));
    }
  }, [showAlert]);

  const handleClickOutside = useCallback((e: MouseEvent) => {
    if (!(e.target as Element).closest(".action-dropdown")) {
      // Don't close the modal if status change is in progress
      if (actionModalState.isStatusLoading) {
        return;
      }
      setActionModalState({ isOpen: false, orgId: null, orgName: null, orgStatus: null, isStatusLoading: false });
    }
  }, [actionModalState.isStatusLoading]);

  useEffect(() => {
    if (actionModalState.isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [actionModalState.isOpen, handleClickOutside]);

  const handleOpenToPublicToggle = () => {
    setToggleConfirmModalState({
      isOpen: true,
      currentState: isOpenToPublic || false
    });
  };

  const confirmOpenToPublicToggle = () => {
    if (onToggleOpenToPublic) {
      onToggleOpenToPublic();
    }
    setToggleConfirmModalState({
      isOpen: false,
      currentState: false
    });
  };

  const filteredData = useMemo(() => {
    return organizations
      .filter((org) => {
        const matchesSearch = Object.values(org)
          .join(" ")
          .toLowerCase()
          .includes(searchTerm.toLowerCase());

        const matchesStatus = !filters.status || org.status === filters.status;

        return matchesSearch && matchesStatus;
      })
      .map((org) => ({
        ...org,
        status: String(org.status),
        cost: org.cost,
        currentCost :org.currentCost,
        credits: org.credits,
        remainingCredits: org.remainingCredits,
        onboardingDate: org.onboardingDate,
        email: org.email,
        admin: org.admin,
        domain: org.domain,
        orgName: org.orgName,
        isDisabled: org.status === 'deleted',
        rowClassName: org.status === 'deleted' ? 'opacity-50 cursor-not-allowed bg-semantic-gray-50' : '',
        menuActions: (
          <div className="flex gap-2 relative p-1">
            {org.status === 'deleted' ? (
              <div className="relative group">
                <button
                  className="p-2 rounded-md bg-semantic-gray-100 cursor-not-allowed opacity-50"
                  disabled
                  onClick={(e) => e.stopPropagation()}
                >
                  <MoreVertical className="w-5 h-5 text-semantic-gray-400" />
                </button>
              </div>
            ) : (
              <button
                className="p-2 rounded-md hover:bg-semantic-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                onClick={(e) => {
                  e.stopPropagation();
                  if (actionModalState.isOpen && actionModalState.orgId === org.id) {
                    // Don't close if status update is in progress
                    if (!actionModalState.isStatusLoading) {
                      setActionModalState({ isOpen: false, orgId: null, orgName: null, orgStatus: null, isStatusLoading: false });
                    }
                  } else {
                    const buttonRect = e.currentTarget.getBoundingClientRect();
                    setActionModalState({
                      isOpen: true,
                      orgId: org.id,
                      orgName: org.orgName,
                      orgStatus: org.status,
                      isStatusLoading: false,
                      position: {
                        top: buttonRect.top + (buttonRect.height / 2) + window.scrollY - 20,
                        left: buttonRect.left + window.scrollX - 192
                      }
                    });
                  }
                }}
              >
                {actionModalState.isOpen && actionModalState.orgId === org.id ? (
                  <X className="w-5 h-5 text-semantic-gray-500" />
                ) : (
                  <MoreVertical className="w-5 h-5 text-semantic-gray-500" />
                )}
              </button>
            )}
          </div>
        ),
      }));
  }, [data, searchTerm, filters, organizations, actionModalState]);

  const renderContent = () => {
    if (isLoading) {
      return  <Loader type="table" />;
    }

    if (error) {
      return (
        <div className="text-center py-8 text-red-600">
          Error loading organizations: {error.message}
        </div>
      );
    }

    if (filteredData.length === 0) {
      if (searchTerm) {
        return (
          <EmptyStateView
            type="noSearchResults"
            onClick={() => {
              setSearchTerm("");
              setCurrentPage(1);
            }}
          />
        );
      }
      if (filters.status) {
        return (
          <EmptyStateView
            type="noFilterResults"
            onClick={() => {
              setFilters({ status: "" });
              setCurrentPage(1);
            }}
          />
        );
      }
      return (
        <EmptyStateView
          type="noOrganizations"
          onClick={() => router.push("/dashboard/organizations/add")}
        />
      );
    }

    return (
      <TableComponent
        headers={[
          ...(isB2C ? [] : [
            { key: "orgName", label: "ORG NAME" },
          ]),
          { key: "admin", label: "ADMIN" },
          { key: "email", label: "EMAIL" },
          { key: "onboardingDate", label: "ONBOARDED DATE" },
          { key: "credits", label: "ALLOCATED CREDITS"},
          { key: "remainingCredits", label: "REMAINING CREDITS"},
          //{key :"currentCost",label:"CURRENT PLAN USAGE"},
          { key: "cost", label: "ORG COST", onClick: routeToCost},
          { key: "status", label: "STATUS" },
          { key: "menuActions", label: "", actionLabel: "" },
        ]}
        data={filteredData}
        onRowClick={handleRowClick}
        sortableColumns={{
          ...(isB2C ? {} : {
            orgName: true,
          }),
          admin: true,
          email: true,
          status: true,
          cost: true,
          //currentCost :true,
          credits: true,
          remainingCredits: true,
          onboardingDate: true,
        }}
        itemsPerPage={rowsPerPage}
      />
    );
  };

  return (
    <div className="bg-white rounded-lg shadow">
      <div className="py-2 border-b flex justify-between items-center">
        <Search searchTerm={searchTerm} setSearchTerm={handleSearch} />
        <div className="flex gap-3 items-center">
          {isB2C && (
            <label htmlFor="openToPublic" className="inline-flex items-center cursor-pointer mr-2">
              <input
                type="checkbox"
                id="openToPublic"
                className="sr-only"
                checked={isOpenToPublic || false}
                onChange={handleOpenToPublicToggle}
              />
              <div className={`relative w-11 h-6 bg-semantic-gray-200 rounded-full peer peer-focus:ring-4 peer-focus:ring-green-300 ${isOpenToPublic ? 'bg-green-500' : ''}`}>
                <div className={`absolute top-[2px] left-[2px] bg-white border border-semantic-gray-300 rounded-full h-5 w-5 transition-all ${isOpenToPublic ? 'translate-x-5' : ''}`}></div>
              </div>
              <span className="ml-3 typography-body-sm font-weight-medium text-semantic-gray-700">Open To Public</span>
            </label>
          )}
          {!isB2C && (
            <DynamicButton
              variant="primary"
              icon={Plus}
              text="Add Organization"
              onClick={() => router.push("/dashboard/organizations/add")}
            />
          )}
          <div className="relative">
            <DynamicButton
              variant="secondary"
              icon={Filter}
              text="Filter"
              onClick={handleFilterClick}
            />
            {isFilterOpen && (
              <FilterDropdown
                filters={filters}
                setFilters={handleFilterChange}
                onClose={() => setIsFilterOpen(false)}
              />
            )}
          </div>
        </div>
      </div>
      {renderContent()}
      <DeleteConfirmationModal
        isOpen={deleteModalState.isOpen}
        onClose={() => setDeleteModalState({ isOpen: false, orgId: null, orgName: null, isLoading: false })}
        onConfirm={() => deleteModalState.orgId && handleDelete(deleteModalState.orgId)}
        title="Delete Organization"
        message={`Are you sure you want to delete ${deleteModalState.orgName}? This action cannot be undone.`}
        isLoading={deleteModalState.isLoading}
      />
      <DeleteConfirmationModal
        isOpen={toggleConfirmModalState.isOpen}
        onClose={() => setToggleConfirmModalState({ isOpen: false, currentState: toggleConfirmModalState.currentState })}
        onConfirm={confirmOpenToPublicToggle}
        title={`${toggleConfirmModalState.currentState ? 'Disable' : 'Enable'} Public Access`}
        message={`Are you sure you want to ${toggleConfirmModalState.currentState ? 'disable' : 'enable'} public access? This will ${toggleConfirmModalState.currentState ? 'restrict' : 'allow'} new users to sign up without invitations.`}
        isLoading={false}
        confirmText={toggleConfirmModalState.currentState ? 'Disable' : 'Enable'}
        confirmButtonClass={toggleConfirmModalState.currentState ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700'}
      />
      {actionModalState.isOpen && (
        <div
          className="fixed z-50 bg-white rounded-lg shadow-lg w-48 action-dropdown"
          style={{
            top: actionModalState.position?.top,
            left: actionModalState.position?.left
          }}
        >
          <button
            className={`absolute -top-2 -right-2 p-1 bg-white rounded-full shadow-md ${actionModalState.isStatusLoading ? 'opacity-50 cursor-not-allowed' : 'hover:bg-semantic-gray-100'}`}
            onClick={() => {
              if (!actionModalState.isStatusLoading) {
                setActionModalState({ isOpen: false, orgId: null, orgName: null, orgStatus: null, isStatusLoading: false });
              }
            }}
            disabled={actionModalState.isStatusLoading}
          >
            <X className="w-3 h-3 text-semantic-gray-500" />
          </button>
          <div className="p-2 space-y-1">
            <button
              className="w-full px-3 py-2 typography-body-sm text-semantic-gray-700 hover:bg-semantic-gray-100 flex items-center gap-2 rounded-md"
              onClick={(e) => {
                e.stopPropagation();
                if (actionModalState.orgId && actionModalState.orgStatus && !actionModalState.isStatusLoading) {
                  handleStatusToggle(actionModalState.orgId, actionModalState.orgStatus);
                }
              }}
              disabled={actionModalState.isStatusLoading}
            >
              {actionModalState.isStatusLoading ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-t-2 border-semantic-gray-500 rounded-full animate-spin"></div>
                  <span className="flex-1 text-left">Processing...</span>
                </div>
              ) : (
                <>
                  <Power className={`w-4 h-4 ${actionModalState.orgStatus === 'active' ? 'text-red-500' : 'text-green-500'}`} />
                  <span className="flex-1 text-left">{actionModalState.orgStatus === 'active' ? 'Deactivate' : 'Activate'}</span>
                </>
              )}
            </button>
            <button
              className="w-full px-3 py-2 typography-body-sm text-red-600 hover:bg-semantic-gray-100 flex items-center gap-2 rounded-md"
              onClick={() => {
                if (actionModalState.orgId && actionModalState.orgName) {
                  setDeleteModalState({
                    isOpen: true,
                    orgId: actionModalState.orgId,
                    orgName: actionModalState.orgName,
                    isLoading: false
                  });
                  setActionModalState({ isOpen: false, orgId: null, orgName: null, orgStatus: null });
                }
              }}
            >
              <Trash className="w-4 h-4 text-red-500" />
              <span className="flex-1 text-left">Delete Organization</span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

// Filter Dropdown Component
interface FilterDropdownProps {
  filters: FilterState;
  setFilters: (filters: FilterState) => void;
  onClose: () => void;
}

const FilterDropdown: React.FC<FilterDropdownProps> = ({
  filters,
  setFilters,
  onClose,
}) => {
  const handleClickOutside = (e: MouseEvent) => {
    if (!(e.target as Element).closest(".filter-dropdown")) {
      onClose();
    }
  };

  React.useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  return (
    <div className="absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg z-50 py-2 filter-dropdown">
      <div className="px-4 py-2">
        <h3 className="typography-body-sm font-weight-medium text-semantic-gray-900">Status</h3>
        <div className="mt-2 space-y-2">
          {["active", "inactive"].map((status) => (
            <label key={status} className="flex items-center">
              <input
                type="checkbox"
                checked={filters.status === status}
                onChange={() =>
                  setFilters({
                    ...filters,
                    status: status === filters.status ? "" : status,
                  })
                }
                className="rounded border-semantic-gray-300"
              />
              <span className="ml-2 typography-body-sm text-semantic-gray-700">
                {status.charAt(0).toUpperCase() + status.slice(1)}
              </span>
            </label>
          ))}
        </div>
      </div>
    </div>
  );
};