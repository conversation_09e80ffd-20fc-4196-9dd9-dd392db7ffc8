// components/Organizations/DetailsSection.tsx
import { LucideIcon } from "lucide-react"
import React from 'react'

interface DetailItem {
 label: string;
 value: string;
}

interface DetailsSectionProps {
 title: string;
 icon: LucideIcon;
 details: DetailItem[];
}

const DetailsSection: React.FC<DetailsSectionProps> = ({ title, icon, details }) => {
 return (
   <div className="bg-white rounded-lg border border-semantic-gray-200 p-6 shadow-sm">
     <div className="flex items-center gap-3 mb-6 pb-3 border-b border-semantic-gray-100">
       {React.createElement(icon, { className: "w-5 h-5 text-semantic-gray-500" })}
       <h3 className="text-semantic-gray-900 font-weight-semibold typography-body">{title}</h3>
     </div>
     
     <div className="grid grid-cols-2 gap-y-4 gap-x-8">
       {details.map((detail, index) => (
         <div key={index} className="flex flex-col space-y-1">
           <span className="typography-body-sm text-semantic-gray-500 font-weight-medium">
             {detail.label}
           </span>
           <span className="typography-body-sm text-semantic-gray-900">
             {detail.value}
           </span>
         </div>
       ))}
     </div>
   </div>
 );
};

export default DetailsSection;