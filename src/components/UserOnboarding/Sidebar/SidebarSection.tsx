// components/layout/Sidebar/SidebarSection.tsx
'use client'
import { useState } from 'react';
import { SidebarItem } from './SidebarItem';

interface SidebarSectionProps {
  title: string;
  icon?: React.ReactNode;
  isActive?: boolean;
  items?: Array<{ 
    label: string; 
    href: string; 
    icon?: React.ReactNode;
    active?: boolean;
  }>;
}

export const SidebarSection = ({ 
  title, 
  icon, 
  items, 
  isActive 
}: SidebarSectionProps) => {
  const [isExpanded, setIsExpanded] = useState(true);

  return (
    <div className="py-2">
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className={`w-full flex items-center px-4 py-2 typography-body-sm font-weight-medium transition-colors duration-200
          ${isActive ? 'bg-semantic-gray-100 text-semantic-gray-900' : 'text-semantic-gray-600 hover:bg-semantic-gray-50'}`}
      >
        {icon && <span className="mr-3 w-5 h-5 flex items-center justify-center">{icon}</span>}
        <span>{title}</span>
        <svg
          className={`ml-auto w-4 h-4 transform transition-transform ${
            isExpanded ? 'rotate-180' : ''
          }`}
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <polyline points="6 9 12 15 18 9" />
        </svg>
      </button>
      
      {isExpanded && items && (
        <div className="mt-1">
          {items.map((item) => (
            <SidebarItem
              key={item.href}
              icon={item.icon}
              label={item.label}
              href={item.href}
              active={item.active}
              className="pl-8"
            />
          ))}
        </div>
      )}
    </div>
  );
};