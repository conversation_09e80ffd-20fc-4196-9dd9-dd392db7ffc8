'use client';

import React, { useState } from "react";
import CountryDropdown from "./CountryDropdown";
import { COUNTRY_CODES } from "@/constants/userOnboarding/countryCodes";

interface ContactNumberFieldProps {
    label?: string;
    type?: string;
    placeholder: string;
    countryCode: string;
    plainNumber: string;
    onCountryCodeChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
    onPlainNumberChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    error?: string;
    onFocus? : () => void;
    onBlur? : () => void;
}

export const ContactNumberField: React.FC<ContactNumberFieldProps> = ({
    label,
    type = 'text',
    placeholder,
    countryCode,
    plainNumber,
    onCountryCodeChange,
    onPlainNumberChange,
    error,
    onFocus,
    onBlur
}) => {
    return (
        <div className="w-full">
            {label &&
                <p className="font-weight-semibold mb-2">{label}</p>
            }
            <div className="flex gap-2">
                <div className="w-20 border-2 border-semantic-gray-300 rounded-lg">
                    <CountryDropdown
                        value={countryCode}
                        onChange={onCountryCodeChange}
                        onFocus={onFocus}
                        onBlur={onBlur}
                    />
                </div>
                <input 
                    type={type} 
                    className="w-full bg-semantic-gray-100 border-semantic-gray-300 border-2 rounded-lg" 
                    placeholder={placeholder} 
                    onChange={onPlainNumberChange}
                    value={plainNumber}
                    onFocus={onFocus}
                    onBlur={onBlur}
                />
            </div>
            { error &&
                <p className="text-red-500">{error}</p>
            }
        </div>
    );
};

export const ContactNumberFieldUI: React.FC<ContactNumberFieldProps> = ({
    label,
    type = 'text', 
    placeholder,
    countryCode,
    plainNumber,
    onCountryCodeChange,
    onPlainNumberChange,
    error,
    onFocus,
    onBlur
}) => {
    const [isOpen, setIsOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState("");

    const filteredCountries = COUNTRY_CODES.filter(country => 
        country.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        country.code.includes(searchTerm)
    );

    return (
        <div className="w-full">
            {label && <p className="font-weight-semibold mb-2">{label}</p>}
            <div className="flex gap-2">
                <div className="relative w-20">
                    <div 
                        className="w-full bg-semantic-gray-100 border-semantic-gray-300 border-2 rounded-lg p-2 cursor-pointer flex justify-between items-center"
                        onClick={() => setIsOpen(!isOpen)}
                    >
                        <span>{countryCode}</span>
                        <svg className={`w-4 h-4 text-semantic-gray-500 transition-transform ${isOpen ? 'rotate-180' : ''}`} 
                             fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 10l5 5 5-5" />
                        </svg>
                    </div>

                    {isOpen && (
                        <div className="absolute z-10 w-48 mt-1 bg-white border-2 border-semantic-gray-300 rounded-lg shadow-lg">
                            <div className="p-2 border-b border-semantic-gray-200">
                                <input
                                    type="text"
                                    placeholder="Search..."
                                    className="w-full p-1 typography-body-sm bg-semantic-gray-50 border border-semantic-gray-200 rounded-md focus:outline-none focus:border-primary"
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                />
                            </div>
                            <div className="max-h-48 overflow-y-auto">
                                {filteredCountries.map((country, index) => (
                                    <div
                                        key={index}
                                        className={`p-2 hover:bg-semantic-gray-100 cursor-pointer flex items-center ${
                                            country.code === countryCode ? 'bg-primary-50 text-primary' : ''
                                        }`}
                                        onClick={() => {
                                            onCountryCodeChange({ target: { value: country.code } } as any);
                                            setIsOpen(false);
                                        }}
                                    >
                                        <span className="w-16">{country.code}</span>
                                        <span className="typography-body-sm">{country.name}</span>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}
                </div>
                <input 
                    type={type}
                    className="w-full bg-semantic-gray-100 border-semantic-gray-300 border-2 rounded-lg p-2"
                    placeholder={placeholder}
                    onChange={onPlainNumberChange}
                    value={plainNumber}
                    onFocus={onFocus}
                    onBlur={onBlur}
                />
            </div>
            {error && <p className="text-red-500 typography-body-sm mt-1">{error}</p>}
        </div>
    );
};