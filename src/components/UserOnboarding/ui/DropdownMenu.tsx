'use client';
import React, { useState } from "react";

interface DropdownItems {
    label: string;
}

interface DropdownMenuProps {
    headLabel: string;
    data: DropdownItems[];
    defaultValue?: string;
    value?: string;  // Added value prop
    onChange?: (value: string) => void;  // Added onChange prop
}

export const DropdownMenu: React.FC<DropdownMenuProps> = ({
    headLabel,
    data,
    defaultValue,
    value,
    onChange
}) => {
    const handleChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
        if (onChange) {
            onChange(event.target.value);
        }
    };

    return (
        <div className="w-[49%]">
            <p className="font-weight-semibold mb-2">{headLabel}</p>
            
            <select
                className="w-full bg-semantic-gray-100 border-semantic-gray-300 border-2 rounded-lg p-2"
                defaultValue={defaultValue}
                value={value}
                onChange={handleChange}
            >
                {data.map((item, index) => (
                    <option key={index} value={item.label}>
                        {item.label}
                    </option>
                ))}
            </select>
        </div>
    );
};



interface DropDownMenuPropsUIProps {
    headLabel: string;
    data: DropdownItems[];
    defaultValue?: string;
    value?: string;
    onChange?: (value: string) => void;
}

export const DropDownMenuPropsUI: React.FC<DropDownMenuPropsUIProps> = ({
    headLabel,
    data,
    defaultValue,
    value,
    onChange
}) => {
    const [searchTerm, setSearchTerm] = useState("");
    const [isOpen, setIsOpen] = useState(false);

    const filteredData = data.filter(item =>
        item.label.toLowerCase().includes(searchTerm.toLowerCase())
    );

    const handleChange = (selectedValue: string) => {
        if (onChange) {
            onChange(selectedValue);
        }
        setIsOpen(false);
    };

    return (
        <div className="w-[49%]">
            <p className="font-weight-semibold mb-2">{headLabel}</p>
            <div className="relative">
                <div 
                    className="w-full bg-semantic-gray-100 border-semantic-gray-300 border-2 rounded-lg p-2 cursor-pointer flex justify-between items-center"
                    onClick={() => setIsOpen(!isOpen)}
                >
                    <span>{value || defaultValue || "Select an option"}</span>
                    <svg className={`w-5 h-5 text-semantic-gray-500 transition-transform ${isOpen ? 'rotate-180' : ''}`} 
                         fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 10l5 5 5-5" />
                    </svg>
                </div>

                {isOpen && (
                    <div className="absolute z-10 w-full mt-1 bg-white border-2 border-semantic-gray-300 rounded-lg shadow-lg">
                        <div className="p-2 border-b border-semantic-gray-200">
                            <input
                                type="text"
                                placeholder="Search..."
                                className="w-full p-2 bg-semantic-gray-50 border border-semantic-gray-200 rounded-md focus:outline-none focus:border-primary"
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                            />
                        </div>
                        <div className="max-h-60 overflow-y-auto">
                            {filteredData.length > 0 ? (
                                filteredData.map((item, index) => (
                                    <div
                                        key={index}
                                        className={`p-2 hover:bg-semantic-gray-100 cursor-pointer ${
                                            item.label === value ? 'bg-primary-50 text-primary' : ''
                                        }`}
                                        onClick={() => handleChange(item.label)}
                                    >
                                        {item.label}
                                    </div>
                                ))
                            ) : (
                                <div className="p-2 text-semantic-gray-500 text-center">No options available</div>
                            )}
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};
