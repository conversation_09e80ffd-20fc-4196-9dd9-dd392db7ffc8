'use client';

import React from "react";

interface FormFieldProps {
    label: string;
    type?: string;
    placeholder: string;
    autocomplete?: boolean;
    onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
    value?: string;
    error?: string;
    onFocus? : () => void;
    onBlur? : () => void;
    helperText?: string;
}

export const FormField: React.FC<FormFieldProps> = ({
    label,
    type = 'text',
    placeholder,
    autocomplete = false,
    onChange,
    value,
    error,
    onFocus,
    onBlur,
    helperText
}) => {
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (type === 'email') {
            e.target.value = e.target.value.toLowerCase();
        }
        if (onChange) {
            onChange(e);
        }
    };

    return (
        <div className="w-[49%]">
            <p className="font-weight-semibold mb-2">{label}</p>
            <input 
                type={type} 
                className="w-full bg-semantic-gray-100 border-semantic-gray-300 border-2 rounded-lg" 
                placeholder={placeholder} 
                autoComplete={autocomplete ? "on" : "off"}
                onChange={handleChange}
                value={value}
                onFocus={onFocus}
                onBlur={onBlur}
            />
            { helperText && !error &&
                <p className="text-semantic-gray-500 typography-body-sm mt-1">{helperText}</p>
            }
            { error &&
                <p className="text-red-500">{error}</p>
            }
        </div>
    );
};