// components/Loader.tsx
import { FC } from 'react';

interface LoaderProps {
  type: 'groups' | 'plans' | 'table' | 'groupDetails' | 'features' | 'permissions';
}

const Loader: FC<LoaderProps> = ({ type }) => {
  const renderGroupsSkeleton = () => (
    <div className="p-6 w-full animate-pulse">
      {/* Header and Search Section */}
      <div className="flex justify-between items-center mb-6">
        <h2 className="h-8 bg-semantic-gray-200 rounded-md w-24"></h2>
        <div className="flex gap-4 items-center">
          <div className="h-10 bg-semantic-gray-200 rounded-lg w-72"></div>
          <div className="h-10 bg-semantic-gray-200 rounded-lg w-32"></div>
        </div>
      </div>

      {/* Grid of Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(9)].map((_, index) => (
          <div key={index} className="border rounded-lg p-4 space-y-4">
            {/* Tag */}
            <div className="h-6 bg-semantic-gray-200 rounded-full w-24"></div>
            
            {/* Title */}
            <div className="h-7 bg-semantic-gray-200 rounded-md w-3/4"></div>
            
            {/* Description */}
            <div className="h-5 bg-semantic-gray-200 rounded-md w-full"></div>
            
            {/* Members and Type row */}
            <div className="flex justify-between items-center pt-2">
              <div className="h-5 bg-semantic-gray-200 rounded-md w-28"></div>
              <div className="h-5 bg-semantic-gray-200 rounded-md w-16"></div>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      <div className="flex justify-between items-center mt-6">
        <div className="h-5 bg-semantic-gray-200 rounded-md w-36"></div>
        <div className="flex gap-2">
          <div className="h-8 w-8 bg-semantic-gray-200 rounded-md"></div>
          <div className="h-8 w-8 bg-semantic-gray-200 rounded-md"></div>
          <div className="h-8 w-8 bg-semantic-gray-200 rounded-md"></div>
        </div>
      </div>
    </div>
  );

  const renderPlansSkeleton = () => (
    <div className="p-6 w-full animate-pulse">
      {/* Toggle Switch */}
      <div className="flex justify-center gap-4 mb-8">
        <div className="h-6 bg-semantic-gray-200 rounded-md w-20"></div>
        <div className="h-6 bg-semantic-gray-200 rounded-md w-16"></div>
      </div>

      {/* Pricing Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-9xl mx-auto">
        {/* Plan Package Card */}
        {[...Array(3)].map((_, index) => (
          <div key={index} className="border rounded-lg p-6 space-y-6">
            {/* Plan Name */}
            <div className="h-7 bg-semantic-gray-200 rounded-md w-1/2 mb-4"></div>
            
            {/* Price */}
            <div className="flex items-baseline gap-2 mb-6">
              <div className="h-10 bg-semantic-gray-200 rounded-md w-24"></div>
              <div className="h-5 bg-semantic-gray-200 rounded-md w-32"></div>
            </div>

            {/* Features List */}
            <div className="space-y-4">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="flex items-center gap-3">
                  <div className="h-5 w-5 bg-semantic-gray-200 rounded-full"></div>
                  <div className="h-4 bg-semantic-gray-200 rounded-md w-full"></div>
                </div>
              ))}
            </div>

            {/* Select Button */}
            <div className="h-10 bg-semantic-gray-200 rounded-md w-full mt-6"></div>
          </div>
        ))}
      </div>

      {/* Bottom Navigation */}
      <div className="flex justify-between items-center mt-8">
        <div className="h-8 bg-semantic-gray-200 rounded-md w-20"></div>
        <div className="flex gap-4">
          <div className="h-8 bg-semantic-gray-200 rounded-md w-24"></div>
          <div className="h-8 bg-semantic-gray-200 rounded-md w-48"></div>
        </div>
      </div>
    </div>
  );

  const renderTableSkeleton = () => (
    <div className="p-6 w-full animate-pulse space-y-6">
      {/* Header and Search Section */}
      <div className="flex justify-between items-center">
        <div className="h-8 bg-semantic-gray-200 rounded-md w-24"></div>
        <div className="flex gap-4 items-center">
          <div className="h-10 bg-semantic-gray-200 rounded-lg w-72 min-w-[280px] flex-shrink-0"></div>
          <div className="flex gap-2">
            <div className="h-10 bg-semantic-gray-200 rounded-lg w-32 min-w-[120px] flex-shrink-0"></div>
            <div className="h-10 bg-semantic-gray-200 rounded-lg w-24 min-w-[100px] flex-shrink-0"></div>
          </div>
        </div>
      </div>
  
      {/* Table Header */}
      <div className="grid grid-cols-5 gap-4 border-b pb-4">
        {['w-32', 'w-48', 'w-36', 'w-40', 'w-24'].map((width, index) => (
          <div key={index} className="flex items-center gap-2">
            <div className={`h-5 bg-semantic-gray-200 rounded-md ${width}`}></div>
            <div className="h-4 w-4 bg-semantic-gray-200 rounded"></div>
          </div>
        ))}
      </div>
  
      {/* Table Rows */}
      <div className="space-y-4">
        {[...Array(10)].map((_, rowIndex) => (
          <div key={rowIndex} className="grid grid-cols-5 gap-4 py-3 border-b">
            <div className="h-5 bg-semantic-gray-200 rounded-md w-32"></div>
            <div className="h-5 bg-semantic-gray-200 rounded-md w-48"></div>
            <div className="h-5 bg-semantic-gray-200 rounded-md w-36"></div>
            <div className="h-5 bg-semantic-gray-200 rounded-md w-40"></div>
            <div className="flex justify-between items-center">
              <div className="h-6 bg-semantic-gray-200 rounded-full w-16"></div>
              <div className="h-8 w-8 bg-semantic-gray-200 rounded-full"></div>
            </div>
          </div>
        ))}
      </div>
  
      {/* Table Footer */}
      <div className="flex justify-between items-center pt-4">
        <div className="flex items-center gap-2">
          <div className="h-8 bg-semantic-gray-200 rounded-md w-32 min-w-[120px] flex-shrink-0"></div>
          <div className="h-8 bg-semantic-gray-200 rounded-md w-16 min-w-[64px] flex-shrink-0"></div>
        </div>
        <div className="flex gap-2">
          {[...Array(3)].map((_, index) => (
            <div key={index} className="h-8 w-8 bg-semantic-gray-200 rounded-md flex-shrink-0"></div>
          ))}
        </div>
      </div>
    </div>
  );
  
  

  const renderGroupDetailsSkeleton = () => (
    <div className="p-6 w-full animate-pulse">
      {/* Breadcrumb */}
      {/* <div className="flex items-center gap-2 mb-6">
        {['w-16', 'w-20', 'w-32'].map((width, index) => (
          <div key={index} className="flex items-center gap-2">
            <div className={`h-4 bg-semantic-gray-200 rounded-md ${width}`}></div>
            {index < 2 && <div className="h-4 w-4 bg-semantic-gray-200 rounded-md"></div>}
          </div>
        ))}
      </div>

      {/* Title and Tag Section */}
      {/* <div className="flex justify-between items-center mb-4">
        <div className="space-y-3">
          <div className="h-8 bg-semantic-gray-200 rounded-md w-64"></div>
          <div className="h-6 bg-semantic-gray-200 rounded-full w-24"></div>
        </div>
        <div className="h-10 bg-semantic-gray-200 rounded-md w-32"></div>
      </div> */}

      {/* Navigation Tabs */}
      {/* <div className="flex gap-6 border-b mb-6">
        {['w-20', 'w-24', 'w-28', 'w-20', 'w-24'].map((width, index) => (
          <div key={index} className={`h-10 bg-semantic-gray-200 rounded-md ${width} mb-4`}></div>
        ))}
      </div> */} 

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {[...Array(3)].map((_, index) => (
          <div key={index} className="border rounded-lg p-6 space-y-3">
            <div className="h-8 bg-semantic-gray-200 rounded-md w-16"></div>
            <div className="h-6 bg-semantic-gray-200 rounded-md w-32"></div>
            <div className="h-4 bg-semantic-gray-200 rounded-md w-48"></div>
          </div>
        ))}
      </div>

      {/* Group Information Section */}
      <div className="border rounded-lg p-6">
        <div className="h-6 bg-semantic-gray-200 rounded-md w-40 mb-6"></div>
        <div className="space-y-6">
          {[...Array(4)].map((_, index) => (
            <div key={index} className="grid grid-cols-2 gap-4">
              <div className="h-5 bg-semantic-gray-200 rounded-md w-32"></div>
              <div className="h-5 bg-semantic-gray-200 rounded-md w-64"></div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderFeaturesSkeleton = () => (
    <div className="p-6 w-full animate-pulse">
      {/* Header Section */}
      <div className="mb-8">
        <div className="h-8 bg-semantic-gray-200 rounded-md w-56 mb-3"></div>
        <div className="h-5 bg-semantic-gray-200 rounded-md w-72"></div>
      </div>

      {/* Global Toggles */}
      <div className="flex justify-between mb-8">
        <div className="space-y-2">
          <div className="h-5 bg-semantic-gray-200 rounded-md w-48"></div>
          <div className="h-6 w-12 bg-semantic-gray-200 rounded-full"></div>
        </div>
        <div className="space-y-2">
          <div className="h-5 bg-semantic-gray-200 rounded-md w-48"></div>
          <div className="h-6 w-12 bg-semantic-gray-200 rounded-full"></div>
        </div>
      </div>

      {/* Feature Cards */}
      <div className="space-y-6">
        {[...Array(3)].map((_, index) => (
          <div key={index} className="border rounded-lg p-6">
            {/* Feature Header */}
            <div className="flex justify-between items-center mb-6">
              <div className="flex items-center gap-2">
                <div className="h-6 bg-semantic-gray-200 rounded-md w-32"></div>
                <div className="h-5 w-5 bg-semantic-gray-200 rounded-full"></div>
              </div>
              <div className="h-6 w-12 bg-semantic-gray-200 rounded-full"></div>
            </div>

            {/* Feature Settings */}
            <div className="grid grid-cols-2 gap-8">
              <div className="space-y-2">
                <div className="h-5 bg-semantic-gray-200 rounded-md w-32"></div>
                <div className="h-10 bg-semantic-gray-200 rounded-md w-full"></div>
              </div>
              <div className="space-y-2">
                <div className="h-5 bg-semantic-gray-200 rounded-md w-32"></div>
                <div className="h-6 w-12 bg-semantic-gray-200 rounded-full"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderPermissionsSkeleton = () => (
    <div className="w-full animate-pulse p-6">
      {/* Table */}
      <div className="border rounded-lg">
        {/* Header Row */}
        <div className="grid grid-cols-6 border-b">
          <div className="p-4 border-r">
            <div className="h-5 bg-semantic-gray-200 rounded-md w-32"></div>
          </div>
          {['Create', 'Read', 'Update', 'Delete', 'Merge/Approval'].map((_, index) => (
            <div key={index} className="p-4 border-r last:border-r-0 flex justify-center">
              <div className="h-5 bg-semantic-gray-200 rounded-md w-24"></div>
            </div>
          ))}
        </div>

        {/* Permission Rows */}
        {[...Array(7)].map((_, rowIndex) => (
          <div key={rowIndex} className="grid grid-cols-6 border-b last:border-b-0">
            <div className="p-4 border-r flex items-center">
              <div className="h-3 bg-semantic-gray-200 rounded-md w-36"></div>
            </div>
            {[...Array(5)].map((_, colIndex) => (
              <div key={colIndex} className="p-4 border-r last:border-r-0 flex justify-center items-center">
                <div className="h-3 w-12 bg-semantic-gray-200 rounded-full"></div>
              </div>
            ))}
          </div>
        ))}
      </div>
    </div>
  );



  const loaderMap = {
    groups: renderGroupsSkeleton,
    plans: renderPlansSkeleton,
    table: renderTableSkeleton,
    groupDetails: renderGroupDetailsSkeleton,
    features: renderFeaturesSkeleton,
    permissions: renderPermissionsSkeleton
  };

  return loaderMap[type]();
};

export default Loader;