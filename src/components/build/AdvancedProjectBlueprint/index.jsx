import React, { useState, useEffect, useRef } from 'react';
import { Plus, Palette, Layout, Trash2 } from 'lucide-react';
import AdvancedTechStackSelection from './AdvancedTechStackSelection';
import { createTechStackUpdate } from './techStackUtils';

// Common EditIcon component
const EditIcon = ({ color = "#F26A1B" }) => (
  <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M6.4165 2.33301H2.33317C2.02375 2.33301 1.72701 2.45592 1.50821 2.67472C1.28942 2.89351 1.1665 3.19026 1.1665 3.49967V11.6663C1.1665 11.9758 1.28942 12.2725 1.50821 12.4913C1.72701 12.7101 2.02375 12.833 2.33317 12.833H10.4998C10.8093 12.833 11.106 12.7101 11.3248 12.4913C11.5436 12.2725 11.6665 11.9758 11.6665 11.6663V7.58301" stroke={color} strokeWidth="1.16667" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M10.7915 1.45814C11.0236 1.22608 11.3383 1.0957 11.6665 1.0957C11.9947 1.0957 12.3094 1.22608 12.5415 1.45814C12.7736 1.6902 12.9039 2.00495 12.9039 2.33314C12.9039 2.66133 12.7736 2.97608 12.5415 3.20814L6.99984 8.74981L4.6665 9.33314L5.24984 6.99981L10.7915 1.45814Z" stroke={color} strokeWidth="1.16667" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

// Default options if not provided via props
const defaultBackendOptions = ["None", "Node.js with Express", "Python with Django", "Ruby on Rails", "PHP with Laravel", "Java Spring Boot"];
const defaultDatabaseOptions = ["None", "PostgreSQL", "MySQL", "MongoDB", "SQLite"];

const architecturePatternOptions = [
  { value: "monolithic-application", label: "Monolithic Application", description: "All functionality in one unified application" },
  { value: "monolithic-service", label: "Monolithic Service", description: "Single service with well-defined external APIs" },
  { value: "multi-container-single-component", label: "Multi-Container Single Component", description: "Multiple containers, each with one component" },
  { value: "multi-container-service", label: "Multi-Container Service", description: "Multiple containers with distinct functionality" },
  { value: "adaptive", label: "Adaptive", description: "Flexible with multiple containers and components" }
];

const getArchitecturePatternLabel = (value) => {
  const option = architecturePatternOptions.find(opt => opt.value === value);
  return option ? option.label : value;
};

const getArchitecturePatternDescription = (value) => {
  const option = architecturePatternOptions.find(opt => opt.value === value);
  return option ? option.description : "";
};

const AdvancedProjectBlueprint = ({ 
  onBlueprintUpdate, 
  initialBlueprint,
  frameworkOptions = [],
  isDarkMode = false
}) => {
  
  // Convert frameworkOptions to frontend options for the dropdown
  // Always include "None" as the first option
  const frontendOptions = ["None"].concat(
    frameworkOptions.length > 0
      ? frameworkOptions
          .filter(f => {
            if (typeof f.type === 'string') {
              return f.type === 'web' || f.type === 'mobile';
            } else if (Array.isArray(f.type)) {
              return f.type.includes('web') || f.type.includes('mobile');
            }
            return false;
          })
          .map(f => f.label)
      : ["React 19", "Next.js", "Vue.js", "Angular", "Svelte"]
  );
  
  // Extract backend frameworks - ones marked with 'backend' type
  const backendFrameworkOptions = ["None"].concat(
    frameworkOptions.length > 0
      ? frameworkOptions
          .filter(f => {
            if (typeof f.type === 'string') {
              return f.type === 'backend';
            } else if (Array.isArray(f.type)) {
              return f.type.includes('backend');
            }
            return false;
          })
          .map(f => f.label)
      : []
  );
  
  // Extract database frameworks - ones marked with 'database' type
  const databaseFrameworkOptions = ["None"].concat(
    frameworkOptions.length > 0
      ? frameworkOptions
          .filter(f => {
            if (typeof f.type === 'string') {
              return f.type === 'database';
            } else if (Array.isArray(f.type)) {
              return f.type.includes('database');
            }
            return false;
          })
          .map(f => f.label)
      : []
  );
  
  // Final backend options combining framework-defined and default options
  const backendOptions = backendFrameworkOptions.length > 1 ? 
    backendFrameworkOptions : defaultBackendOptions;
  
  // Final database options combining framework-defined and default options
  const databaseOptions = databaseFrameworkOptions.length > 1 ? 
    databaseFrameworkOptions : defaultDatabaseOptions;
  
  // Generate initial blueprint - use initialBlueprint if provided
  const generateInitialBlueprint = (initialBlueprint) => {
    if (initialBlueprint) {
      // Check if we need to set frontend to "None" if a backend framework was selected
      const frontendValue = initialBlueprint.techStack?.frontend?.[0] || "None";
      const backendValue = initialBlueprint.techStack?.backend?.[0] || "None";
      
      // If only backend is selected, ensure frontend is "None" and vice versa
      const isBackendOnly = backendOptions.includes(frontendValue) && frontendValue !== "None";
      const isFrontendOnly = frontendOptions.includes(backendValue) && backendValue !== "None";
      
      // Ensure all features have proper IDs
      const featuresWithIds = (initialBlueprint.features || []).map((feature, index) => ({
        ...feature,
        id: feature.id || Math.random().toString(36).substring(2, 9)
      }));
      
      // Create a copy of the initialBlueprint with potentially adjusted techStack
      return {
        ...initialBlueprint,
        features: featuresWithIds,
        techStack: {
          ...initialBlueprint.techStack,
          frontend: [isBackendOnly ? "None" : frontendValue],
          backend: [isFrontendOnly ? "None" : backendValue],
          database: [initialBlueprint.techStack?.database?.[0] || databaseOptions[1] || "PostgreSQL"]
        },
        colors: {
          primary: "#F15A24",
          secondary: "#F97316",
          accent: "#64748B"
        },
        // Set default architecture pattern if not provided or invalid
        architecturePattern: initialBlueprint.architecturesPattern && 
                           architecturePatternOptions.some(opt => opt.value === initialBlueprint.architecturePattern)
                           ? initialBlueprint.architecturePattern 
                           : "monolithic-application"
      };
    }
    
    return {
      id: Math.random().toString(36).substring(2, 9),
      name: "Web Application",
      description: "A modern web application with user-friendly interface",
      features: [
        {
          id: "1",
          name: "Rich Text Editing",
          description: "Fullscreen banner with headline, subtext, and primary CTA button",
          isEnabled: true
        },
        {
          id: "2",
          name: "Real-time Grammar Check",
          description: "Visual presentation of product features with icons and benefits",
          isEnabled: true
        },
        {
          id: "3",
          name: "Smart Auto-completion",
          description: "Customer reviews with photos, ratings, and testimonial quotes",
          isEnabled: true
        }
      ],
      techStack: {
        frontend: [frontendOptions[1] || "React 19"], // Skip "None" option for default
        backend: [backendOptions[1] || "Node.js with Express"], // Skip "None" option for default
        database: [databaseOptions[1] || "PostgreSQL"] // Skip "None" option for default
      },
      colors: {
        primary: "#F15A24",
        secondary: "#F97316",
        accent: "#64748B"
      },
      theme: "light",
      estimatedTime: "2-4 weeks",
      complexity: "medium",
      layoutDescription: "Clean and minimalist design with clear sections for each feature. The layout will use a responsive grid system to ensure proper display on all device sizes.",
      architecturePattern: "monolithic-application" // Default architecture pattern
    };
  };

  // Use a function in useState to ensure this only runs once on mount
  const [blueprint, setBlueprint] = useState(() => generateInitialBlueprint(initialBlueprint));
  
  // Keep track of the last blueprint we sent to the parent
  const lastUpdatedRef = useRef("");
  // Flag to prevent updating on initial blueprint changes
  const initialUpdateDone = useRef(false);
  
  // Update local state when initialBlueprint changes - but only once
  useEffect(() => {
    if (initialBlueprint && !initialUpdateDone.current) {
      initialUpdateDone.current = true;
      setBlueprint(generateInitialBlueprint(initialBlueprint));
    }
  }, [initialBlueprint]);
  
  // Update parent component when blueprint changes
  useEffect(() => {
    // Only notify parent if this is a meaningful change
    const blueprintJson = JSON.stringify(blueprint);
    if (onBlueprintUpdate && blueprintJson !== lastUpdatedRef.current) {
      lastUpdatedRef.current = blueprintJson;
      onBlueprintUpdate(blueprint);
    }
  }, [blueprint, onBlueprintUpdate]);

  const [editingOverview, setEditingOverview] = useState(false);
  const [editingFeature, setEditingFeature] = useState(null);
  const [editingLayout, setEditingLayout] = useState(null);
  const [newFeature, setNewFeature] = useState({ name: "", description: "" });
  const [newLayout, setNewLayout] = useState({ name: "", description: "" });
  const [addingFeature, setAddingFeature] = useState(false);
  const [addingLayout, setAddingLayout] = useState(false);
  const [editingLayoutDescription, setEditingLayoutDescription] = useState(false);
  const [deleteConfirmId, setDeleteConfirmId] = useState(null);
  const [architectureDropdownOpen, setArchitectureDropdownOpen] = useState(false);
  const [editingTitle, setEditingTitle] = useState(false);

  // Color picker refs to programmatically open color dialogs
  const primaryColorRef = useRef(null);
  const secondaryColorRef = useRef(null);
  const accentColorRef = useRef(null);
  const architectureDropdownRef = useRef(null);

  // Handle click outside for architecture dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (architectureDropdownRef.current && !architectureDropdownRef.current.contains(event.target)) {
        setArchitectureDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Handle overview edit
  const handleOverviewChange = (e) => {
    setBlueprint((prev) => ({
      ...prev,
      description: e.target.value
    }));
  };

  // Handle project title edit
  const handleTitleChange = (e) => {
    setBlueprint((prev) => ({
      ...prev,
      name: e.target.value
    }));
  };

  // Handle feature toggle
  const handleFeatureToggle = (featureId) => {
    setBlueprint((prev) => ({
      ...prev,
      features: prev.features.map((feature) =>
        feature.id === featureId ? { ...feature, isEnabled: !feature.isEnabled } : feature
      )
    }));
  };

  // Handle feature edit
  const handleFeatureEdit = (featureId, field, value) => {
    setBlueprint((prev) => {
      const updatedFeatures = prev.features.map((feature) => {
        if (feature.id === featureId) {
          return { ...feature, [field]: value };
        }
        return feature;
      });
      
      return {
        ...prev,
        features: updatedFeatures
      };
    });
  };

  // Handle feature delete
  const handleDeleteFeature = (featureId) => {
    setBlueprint((prev) => {
      const filteredFeatures = prev.features.filter((feature) => feature.id !== featureId);
      
      return {
        ...prev,
        features: filteredFeatures
      };
    });
    setDeleteConfirmId(null);
  };

  // Handle tech stack change
  const handleTechStackChange = (category, value) => {
    setBlueprint((prev) => {
      const newTechStack = createTechStackUpdate(
        prev.techStack,
        category,
        value,
        backendOptions
      );
      
      return {
        ...prev,
        techStack: newTechStack
      };
    });
  };

  // Handle color change
  const handleColorChange = (colorType, value) => {
    setBlueprint((prev) => ({
      ...prev,
      colors: {
        ...prev.colors,
        [colorType]: value
      }
    }));
  };

  // Handle theme change
  const handleThemeChange = (theme) => {
    setBlueprint((prev) => ({
      ...prev,
      theme
    }));
  };

  // Handle layout description change
  const handleLayoutDescriptionChange = (e) => {
    setBlueprint((prev) => ({
      ...prev,
      layoutDescription: e.target.value
    }));
  };

  // Handle adding a new feature
  const handleAddFeature = () => {
    if (newFeature.name.trim() === "") return;

    setBlueprint((prev) => ({
      ...prev,
      features: [
        ...prev.features,
        {
          id: Math.random().toString(36).substring(2, 9),
          name: newFeature.name,
          description: newFeature.description,
          isEnabled: true
        }
      ]
    }));

    setNewFeature({ name: "", description: "" });
    setAddingFeature(false);
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      {/* Project Title */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-lg font-medium text-gray-800">Project Title</h3>
          {!editingTitle && (
            <button 
              onClick={() => setEditingTitle(true)}
              className="text-[#f26a1b] hover:text-[#e05a0f] flex items-center"
            >
              <EditIcon />
              <span className="ml-1">Edit</span>
            </button>
          )}
        </div>
        
        {editingTitle ? (
          <div>
            <div className="border border-gray-200 rounded-lg mb-2">
              <input
                type="text"
                value={blueprint.name}
                onChange={handleTitleChange}
                className="w-full py-2 px-3 border-0 focus:ring-0 focus:outline-none"
                placeholder="Enter project title"
                autoFocus
              />
            </div>
            <div className="flex justify-end">
              <button 
                onClick={() => setEditingTitle(false)}
                className="px-3 py-1 bg-[#f26a1b] text-white hover:bg-[#e05a0f] rounded-md text-sm flex items-center"
              >
                <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mr-1">
                  <path d="M20 6L9 17L4 12" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                Save
              </button>
            </div>
          </div>
        ) : (
          <div className="py-2 px-3 border border-gray-200 rounded-lg bg-gray-50">
            <h2 className="text-lg font-medium text-gray-800">{blueprint.name}</h2>
          </div>
        )}
      </div>
      
      {/* Project Overview */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-lg font-medium text-gray-800">Project Overview</h3>
          <button 
            onClick={() => setEditingOverview(!editingOverview)}
            className="text-[#f26a1b] hover:text-[#e05a0f] flex items-center"
          >
            <EditIcon />
            <span className="ml-1">Edit</span>
          </button>
        </div>
        
        {editingOverview ? (
          <div className="border border-gray-200 rounded-lg p-2">
            <textarea
              value={blueprint.description}
              onChange={handleOverviewChange}
              className="w-full p-2 rounded-md border-0 focus:ring-1 focus:ring-[#f26a1b]"
              rows={4}
            />
            <div className="flex justify-end mt-2">
              <button 
                onClick={() => setEditingOverview(false)}
                className="px-3 py-1 bg-[#f26a1b] text-white rounded-md hover:bg-[#e05a0f] text-sm flex items-center"
              >
                <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mr-1">
                  <path d="M20 6L9 17L4 12" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                Save
              </button>
            </div>
          </div>
        ) : (
          <div className="p-4 border border-gray-200 rounded-lg">
            <p className="text-gray-700">{blueprint.description}</p>
          </div>
        )}
      </div>
      
      {/* Core Features */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-gray-800">Core Features</h3>
          <button 
            onClick={() => setAddingFeature(true)}
            className="flex items-center text-[#f26a1b] hover:text-[#e05a0f]"
          >
            <Plus size={16} className="mr-1" />
            Add Feature
          </button>
        </div>
        
        <div className="space-y-4">
          {/* Add Feature Form */}
          {addingFeature && (
            <div className="p-4 bg-neutral-100 rounded-lg mb-4">
              <div className="mb-3">
                <label className="block text-sm font-medium text-neutral-900 mb-1">Feature Name</label>
                <input
                  type="text"
                  value={newFeature.name}
                  onChange={(e) => setNewFeature({...newFeature, name: e.target.value})}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-[#f26a1b] focus:border-[#f26a1b] outline-none"
                  placeholder="Feature name"
                />
              </div>
              <div className="mb-4">
                <label className="block text-sm font-medium text-neutral-900 mb-1">Description</label>
                <input
                  type="text"
                  value={newFeature.description}
                  onChange={(e) => setNewFeature({...newFeature, description: e.target.value})}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-[#f26a1b] focus:border-[#f26a1b] outline-none"
                  placeholder="Describe the feature"
                />
              </div>
              <div className="flex justify-end space-x-2">
                <button 
                  onClick={() => setAddingFeature(false)}
                  className="px-3 py-1.5 border border-gray-300 text-gray-600 rounded-md hover:bg-gray-50 text-sm"
                >
                  Cancel
                </button>
                <button 
                  onClick={handleAddFeature}
                  className="px-3 py-1.5 bg-[#f26a1b] text-white rounded-md hover:bg-[#e05a0f] text-sm"
                  disabled={!newFeature.name.trim()}
                >
                  Add Feature
                </button>
              </div>
            </div>
          )}
          
          {/* Feature List */}
          {blueprint.features.map((feature, index) => (
            <div key={feature.id} className="flex items-center justify-between bg-[#f9f9f9] rounded-lg p-4 mb-2">
              {editingFeature === feature.id ? (
                <div className="w-full">
                  <div className="mb-3">
                    <label className="block text-sm font-medium text-neutral-900 mb-1">Feature Name</label>
                    <input
                      type="text"
                      value={feature.name}
                      onChange={(e) => handleFeatureEdit(feature.id, 'name', e.target.value)}
                      className="w-full p-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-[#f26a1b] focus:border-[#f26a1b] outline-none"
                    />
                  </div>
                  <div className="mb-3">
                    <label className="block text-sm font-medium text-neutral-900 mb-1">Description</label>
                    <input
                      type="text"
                      value={feature.description}
                      onChange={(e) => handleFeatureEdit(feature.id, 'description', e.target.value)}
                      className="w-full p-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-[#f26a1b] focus:border-[#f26a1b] outline-none"
                    />
                  </div>
                  <div className="flex justify-end">
                    <button 
                      onClick={() => setEditingFeature(null)}
                      className="px-3 py-1.5 bg-[#f26a1b] text-white rounded-md hover:bg-[#e05a0f] text-sm flex items-center"
                    >
                      <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mr-1">
                        <path d="M20 6L9 17L4 12" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                      Save
                    </button>
                  </div>
                </div>
              ) : deleteConfirmId === feature.id ? (
                <div className="w-full">
                  <div className="flex items-center justify-between">
                    <p className="text-sm text-gray-700">Are you sure you want to delete this feature?</p>
                    <div className="flex space-x-2">
                      <button 
                        onClick={() => setDeleteConfirmId(null)}
                        className="px-3 py-1 border border-gray-300 text-gray-600 rounded-md hover:bg-gray-50 text-xs"
                      >
                        Cancel
                      </button>
                      <button 
                        onClick={() => handleDeleteFeature(feature.id)}
                        className="px-3 py-1 bg-red-500 text-white rounded-md hover:bg-red-600 text-xs flex items-center"
                      >
                        <Trash2 size={12} className="mr-1" />
                        Delete
                      </button>
                    </div>
                  </div>
                </div>
              ) : (
                <>
                  <div className="flex items-start">
                    {/* Generic feature icon */}
                    <div className="mr-4 text-[#f26a1b]">
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 11H15M9 15H12M12 3H7C5.89543 3 5 3.89543 5 5V19C5 20.1046 5.89543 21 7 21H17C18.1046 21 19 20.1046 19 19V9.82843C19 9.29799 18.7893 8.78929 18.4142 8.41421L13.5858 3.58579C13.2107 3.21071 12.702 3 12.1716 3H12Z" stroke="#f26a1b" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </div>
                    
                    {/* Feature content */}
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">{feature.name}</h4>
                      <p className="text-xs text-gray-600">{feature.description}</p>
                    </div>
                  </div>
                  
                  {/* Action buttons */}
                  <div className="flex space-x-2">
                    <button 
                      onClick={() => setEditingFeature(feature.id)}
                      className="text-[#f26a1b] hover:text-[#e05a0f]"
                      aria-label="Edit feature"
                    >
                      <EditIcon />
                    </button>
                    <button 
                      onClick={() => setDeleteConfirmId(feature.id)}
                      className="text-gray-400 hover:text-red-500"
                      aria-label="Delete feature"
                    >
                      <Trash2 size={14} />
                    </button>
                  </div>
                </>
                              )}
              </div>
            ))}
        </div>
      </div>
      
      {/* Architecture Pattern */}
      <div className="mb-6">
        <h3 className="text-lg font-medium mb-4 text-gray-800">Architecture Pattern</h3>
        
        <div className="relative" ref={architectureDropdownRef}>
          {/* Custom Dropdown Button */}
          <button
            type="button"
            onClick={() => setArchitectureDropdownOpen(!architectureDropdownOpen)}
            className="w-full p-3 border border-gray-200 rounded-lg bg-white text-left flex items-center justify-between hover:border-gray-300 transition-colors"
          >
            <div>
              <div className="text-gray-900 font-medium">{getArchitecturePatternLabel(blueprint.architecturePattern)}</div>
              <div className="text-sm text-gray-500 mt-0.5">
                {getArchitecturePatternDescription(blueprint.architecturePattern)}
              </div>
            </div>
            <svg 
              className={`w-5 h-5 text-gray-400 transition-transform ${architectureDropdownOpen ? 'transform rotate-180' : ''}`} 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>
          
          {/* Dropdown Menu */}
          {architectureDropdownOpen && (
            <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
              {architecturePatternOptions.map((option) => (
                <button
                  key={option.value}
                  type="button"
                  onClick={() => {
                    setBlueprint((prev) => ({
                      ...prev,
                      architecturePattern: option.value
                    }));
                    setArchitectureDropdownOpen(false);
                  }}
                  className={`w-full px-4 py-3 text-left transition-colors relative ${
                    blueprint.architecturePattern === option.value 
                      ? 'bg-[#FFF4F0]' 
                      : 'hover:bg-gray-50'
                  } ${option.value === "monolithic-application" ? 'rounded-t-lg' : ''} ${
                    option.value === "adaptive" ? 'rounded-b-lg' : 'border-b border-gray-100'
                  } ${option.value === "monolithic-application" && blueprint.architecturePattern !== option.value ? 'hover:rounded-t-lg' : ''} ${
                    option.value === "adaptive" && blueprint.architecturePattern !== option.value ? 'hover:rounded-b-lg' : ''
                  }`}
                >
                  {/* Orange left border for selected item */}
                  {blueprint.architecturePattern === option.value && (
                    <div className={`absolute left-0 top-0 bottom-0 w-1 bg-[#F26522] ${
                      option.value === "monolithic-application" ? 'rounded-tl-lg' : ''
                    } ${option.value === "adaptive" ? 'rounded-bl-lg' : ''}`} />
                  )}
                  <div className="text-gray-900 font-medium">{option.label}</div>
                  <div className="text-sm text-gray-500 mt-0.5">{option.description}</div>
                </button>
              ))}
            </div>
          )}
        </div>
      </div>
      
      {/* Tech Stack */}
      <div className="mb-6">
        <AdvancedTechStackSelection 
          techStack={blueprint.techStack}
          onChange={(category, value) => handleTechStackChange(category, value)}
          frontendOptions={frontendOptions}
          backendOptions={backendOptions}
          databaseOptions={databaseOptions}
          isDarkMode={false}
        />
      </div>
      
      {/* Style Guidelines */}
      <div className="mb-6">
        <h3 className="text-lg font-medium mb-4 text-gray-800">Style Guidelines</h3>
        
        {/* Color Palette - Placed inside Style Guidelines */}
        <div className="mb-6">
          <div className="flex items-start">
            <div className="w-8 mr-4 text-semantic-gray-500 flex-shrink-0">
              <Palette size={24} className="text-primary" />
            </div>
            <div className="flex-1">
              <div className="flex space-x-8 items-center">
                {/* Primary Color */}
                <div className="text-center relative">
                  <div className="relative w-8 h-8 mb-2 mx-auto">
                    <div 
                      className="w-8 h-8 rounded-full absolute top-0 left-0"
                      style={{ backgroundColor: blueprint.colors?.primary || '#F15A24' }}
                      key={`primary-display-${blueprint.colors?.primary}`}
                    />
                    <button
                      className="w-8 h-8 rounded-full absolute top-0 left-0 z-10 opacity-0 hover:opacity-100 flex items-center justify-center hover:bg-semantic-gray-900 hover:bg-opacity-50 transition-opacity duration-200 cursor-pointer border-0 p-0"
                      onClick={() => primaryColorRef.current?.click()}
                    >
                      <EditIcon color="white" />
                    </button>
                  </div>
                  <p className="text-xs text-semantic-gray-500">Primary</p>
                  <input
                    ref={primaryColorRef}
                    type="color"
                    value={blueprint.colors?.primary || '#F15A24'}
                    onChange={(e) => handleColorChange('primary', e.target.value)}
                    className="absolute opacity-0 pointer-events-none"
                    aria-label="Select primary color"
                  />
                </div>
                
                {/* Secondary Color */}
                <div className="text-center relative">
                  <div className="relative w-8 h-8 mb-2 mx-auto">
                    <div 
                      className="w-8 h-8 rounded-full absolute top-0 left-0"
                      style={{ backgroundColor: blueprint.colors?.secondary || '#F97316' }}
                      key={`secondary-display-${blueprint.colors?.secondary}`}
                    />
                    <button 
                      className="w-8 h-8 rounded-full absolute top-0 left-0 z-10 opacity-0 hover:opacity-100 flex items-center justify-center hover:bg-black hover:bg-opacity-50 transition-opacity duration-200 cursor-pointer border-0 p-0"
                      onClick={() => secondaryColorRef.current?.click()}
                    >
                      <EditIcon color="white" />
                    </button>
                  </div>
                  <p className="text-xs text-gray-500">Secondary</p>
                  <input 
                    ref={secondaryColorRef}
                    type="color" 
                    value={blueprint.colors?.secondary || '#F97316'}
                    onChange={(e) => handleColorChange('secondary', e.target.value)}
                    className="absolute opacity-0 pointer-events-none"
                    aria-label="Select secondary color"
                  />
                </div>
                
                {/* Accent Color */}
                <div className="text-center relative">
                  <div className="relative w-8 h-8 mb-2 mx-auto">
                    <div 
                      className="w-8 h-8 rounded-full absolute top-0 left-0"
                      style={{ backgroundColor: blueprint.colors?.accent || '#64748B' }}
                      key={`accent-display-${blueprint.colors?.accent}`}
                    />
                    <button
                      className="w-8 h-8 rounded-full absolute top-0 left-0 z-10 opacity-0 hover:opacity-100 flex items-center justify-center hover:bg-semantic-gray-900 hover:bg-opacity-50 transition-opacity duration-200 cursor-pointer border-0 p-0"
                      onClick={() => accentColorRef.current?.click()}
                    >
                      <EditIcon color="white" />
                    </button>
                  </div>
                  <p className="text-xs text-semantic-gray-500">Accent</p>
                  <input
                    ref={accentColorRef}
                    type="color"
                    value={blueprint.colors?.accent || '#64748B'}
                    onChange={(e) => handleColorChange('accent', e.target.value)}
                    className="absolute opacity-0 pointer-events-none"
                    aria-label="Select accent color"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Page Layout */}
        <div className="mb-6">
          <div className="flex items-start">
            <div className="w-8 mr-4 text-gray-500 flex-shrink-0">
              <Layout size={24} className="text-[#f26a1b]" />
            </div>
            <div className="flex-1">
              <div className="flex justify-between items-center mb-2">
                <h4 className="font-medium text-sm text-gray-800">Page Layout</h4>
                <button 
                  onClick={() => setEditingLayoutDescription(!editingLayoutDescription)}
                  className="text-[#f26a1b] hover:text-[#e05a0f] flex items-center"
                >
                   <EditIcon />
                   <span className="ml-1">Edit</span>
                </button>
              </div>
              
              {editingLayoutDescription ? (
                <div className="border border-gray-200 rounded-lg p-2">
                  <textarea
                    value={blueprint.layoutDescription}
                    onChange={handleLayoutDescriptionChange}
                    className="w-full p-2 rounded-md border-0 focus:ring-1 focus:ring-[#f26a1b]"
                    rows={4}
                  />
                  <div className="flex justify-end mt-2">
                    <button 
                      onClick={() => setEditingLayoutDescription(false)}
                      className="px-3 py-1 bg-[#f26a1b] text-white rounded-md hover:bg-[#e05a0f] text-sm flex items-center"
                    >
                      <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mr-1">
                        <path d="M20 6L9 17L4 12" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                      Save
                    </button>
                  </div>
                </div>
              ) : (
                <div className="p-4 border border-gray-200 rounded-lg">
                  <p className="text-gray-700">{blueprint.layoutDescription}</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      
      {/* Footer Info */}
      <div className="mt-8 p-4 bg-gray-50 rounded-lg">
        <p className="text-sm text-gray-600 text-center">
          Kavia AI will generate your application based on these specifications
        </p>
      </div>
    </div>
  );
};

export default AdvancedProjectBlueprint;