// src/components/build/ProjectCreationFlow/ProjectBlueprint/index.tsx
// Updated to handle backend-only projects properly

import React, { useState, useEffect, useRef } from 'react';
import { Plus, Palette, Layout, Sun, Trash2 } from 'lucide-react';
import { ProjectBlueprint as ProjectBlueprintType, FrameworkOption } from '../types';
import TechStackSelection from './TechStackSelection';
import { createTechStackUpdate } from './techStackUtils';
import ExternalIntegrations from './ExternalIntegration';

// Common EditIcon component
const EditIcon = ({ color = "#F26A1B" }) => (
  <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M6.4165 2.33301H2.33317C2.02375 2.33301 1.72701 2.45592 1.50821 2.67472C1.28942 2.89351 1.1665 3.19026 1.1665 3.49967V11.6663C1.1665 11.9758 1.28942 12.2725 1.50821 12.4913C1.72701 12.7101 2.02375 12.833 2.33317 12.833H10.4998C10.8093 12.833 11.106 12.7101 11.3248 12.4913C11.5436 12.2725 11.6665 11.9758 11.6665 11.6663V7.58301" stroke={color} strokeWidth="1.16667" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M10.7915 1.45814C11.0236 1.22608 11.3383 1.0957 11.6665 1.0957C11.9947 1.0957 12.3094 1.22608 12.5415 1.45814C12.7736 1.6902 12.9039 2.00495 12.9039 2.33314C12.9039 2.66133 12.7736 2.97608 12.5415 3.20814L6.99984 8.74981L4.6665 9.33314L5.24984 6.99981L10.7915 1.45814Z" stroke={color} strokeWidth="1.16667" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

// Default options if not provided via props
const defaultBackendOptions = ["None", "Node.js with Express", "Python with Django", "Ruby on Rails", "PHP with Laravel", "Java Spring Boot", "FastAPI"];
const defaultDatabaseOptions = ["None", "PostgreSQL", "MySQL", "MongoDB", "SQLite"];

interface ProjectBlueprintProps {
  onBlueprintUpdate?: (blueprint: ProjectBlueprintType) => void;
  initialBlueprint?: ProjectBlueprintType;
  frameworkOptions?: FrameworkOption[];
  isDarkMode?: boolean;
}

const ProjectBlueprint: React.FC<ProjectBlueprintProps> = ({
  onBlueprintUpdate,
  initialBlueprint,
  frameworkOptions = [],
  isDarkMode = false
}) => {

  // Convert frameworkOptions to frontend options for the dropdown
  // Always include "None" as the first option
  const frontendOptions = ["None"].concat(
    frameworkOptions.length > 0
      ? frameworkOptions
          .filter(f => {
            if (typeof f.type === 'string') {
              return f.type === 'web' || f.type === 'mobile';
            } else if (Array.isArray(f.type)) {
              return f.type.includes('web') || f.type.includes('mobile');
            }
            return false;
          })
          .map(f => f.label)
      : ["React 19", "Next.js", "Vue.js", "Angular", "Slidev", "Svelte"]
  );

  // Extract backend frameworks - ones marked with 'backend' type
  const backendFrameworkOptions = ["None"].concat(
    frameworkOptions.length > 0
      ? frameworkOptions
          .filter(f => {
            if (typeof f.type === 'string') {
              return f.type === 'backend';
            } else if (Array.isArray(f.type)) {
              return f.type.includes('backend');
            }
            return false;
          })
          .map(f => f.label)
      : []
  );

  // Extract database frameworks - ones marked with 'database' type
  const databaseFrameworkOptions = ["None"].concat(
    frameworkOptions.length > 0
      ? frameworkOptions
          .filter(f => {
            if (typeof f.type === 'string') {
              return f.type === 'database';
            } else if (Array.isArray(f.type)) {
              return f.type.includes('database');
            }
            return false;
          })
          .map(f => f.label)
      : []
  );

  // Final backend options combining framework-defined and default options
  const backendOptions = backendFrameworkOptions.length > 1 ?
    backendFrameworkOptions : defaultBackendOptions;

  // Final database options combining framework-defined and default options
  const databaseOptions = databaseFrameworkOptions.length > 1 ?
    databaseFrameworkOptions : defaultDatabaseOptions;

  // Generate initial blueprint - use initialBlueprint if provided
  const generateInitialBlueprint = (): ProjectBlueprintType => {
    if (initialBlueprint) {
      // Check if we need to set frontend to "None" if a backend framework was selected
      const frontendValue = initialBlueprint.techStack?.frontend?.[0] || "None";
      const backendValue = initialBlueprint.techStack?.backend?.[0] || "None";

      // If only backend is selected, ensure frontend is "None" and vice versa
      const isBackendOnly = backendOptions.includes(frontendValue) && frontendValue !== "None";
      const isFrontendOnly = frontendOptions.includes(backendValue) && backendValue !== "None";

      // Create a copy of the initialBlueprint with potentially adjusted techStack
      return {
        ...initialBlueprint,
        techStack: {
          ...initialBlueprint.techStack,
          frontend: [isBackendOnly ? "None" : frontendValue],
          backend: [isFrontendOnly ? "None" : backendValue]
        }
      };
    }

    return {
      id: Math.random().toString(36).substring(2, 9),
      name: "Web Application",
      description: "A modern web application with user-friendly interface",
      features: [
        {
          id: "1",
          name: "Rich Text Editing",
          description: "Fullscreen banner with headline, subtext, and primary CTA button",
          isEnabled: true
        },
        {
          id: "2",
          name: "Real-time Grammar Check",
          description: "Visual presentation of product features with icons and benefits",
          isEnabled: true
        },
        {
          id: "3",
          name: "Smart Auto-completion",
          description: "Customer reviews with photos, ratings, and testimonial quotes",
          isEnabled: true
        }
      ],
      colors: {
        primary: "#F15A24",
        secondary: "#F97316",
        accent: "#64748B"
      },
      techStack: {
        frontend: [frontendOptions[1] || "React 19"], // Skip "None" option for default
        backend: [backendOptions[1] || "Node.js with Express"], // Skip "None" option for default
        database: [databaseOptions[1] || "PostgreSQL"] // Skip "None" option for default
      },
      theme: "light",
      estimatedTime: "2-4 weeks",
      complexity: "medium",
      layoutDescription: "Clean and minimalist design with clear sections for each feature. The layout will use a responsive grid system to ensure proper display on all device sizes."
    };
  };

  // Use a function in useState to ensure this only runs once on mount
  const [blueprint, setBlueprint] = useState<ProjectBlueprintType>(() => generateInitialBlueprint());
  
  // Determine if this is a backend-only project
  const isBackendOnly = (blueprint as any).isBackendOnly || 
    (blueprint.techStack?.frontend?.[0] === "None" && 
     blueprint.techStack?.backend?.[0] !== "None" &&
     blueprint.techStack?.backend?.[0] !== undefined);

  // Update blueprint with isBackendOnly flag only
  useEffect(() => {
    const newIsBackendOnly = blueprint.techStack?.frontend?.[0] === "None" && 
                            blueprint.techStack?.backend?.[0] !== "None" &&
                            blueprint.techStack?.backend?.[0] !== undefined;
    
    if ((blueprint as any).isBackendOnly !== newIsBackendOnly) {
      setBlueprint(prev => ({
        ...prev,
        isBackendOnly: newIsBackendOnly
      } as ProjectBlueprintType));
    }
  }, [blueprint.techStack]);
  
  // Local state for architecture pattern (separate from blueprint)
  const [selectedArchitecturePattern, setSelectedArchitecturePattern] = useState(
    isBackendOnly ? "api-architecture" : "monolithic-application"
  );
  const [architectureDropdownOpen, setArchitectureDropdownOpen] = useState(false);

  // Keep track of the last blueprint we sent to the parent
  const lastUpdatedRef = useRef<string>("");
  // Flag to prevent updating on initial blueprint changes
  const initialUpdateDone = useRef<boolean>(false);

  // Update parent component when blueprint changes
  useEffect(() => {
    // Only notify parent if this is a meaningful change
    const blueprintJson = JSON.stringify(blueprint);
    if (onBlueprintUpdate && blueprintJson !== lastUpdatedRef.current) {
      lastUpdatedRef.current = blueprintJson;
      onBlueprintUpdate(blueprint);
    }
  }, [blueprint, onBlueprintUpdate]);

  // Update local state when initialBlueprint changes
  useEffect(() => {
    if (initialBlueprint && !initialUpdateDone.current) {
      initialUpdateDone.current = true;

      // Check if we need to set frontend to "None" if a backend framework was selected
      const frontendValue = initialBlueprint.techStack?.frontend?.[0] || "None";
      const backendValue = initialBlueprint.techStack?.backend?.[0] || "None";
      const databaseValue = initialBlueprint.techStack?.database?.[0] || databaseOptions[1] || "PostgreSQL";

      // If only backend is selected, ensure frontend is "None" and vice versa
      const isBackendOnly = backendOptions.includes(frontendValue) && frontendValue !== "None";
      const isFrontendOnly = frontendOptions.includes(backendValue) && backendValue !== "None";

      // Create a copy with potentially adjusted techStack
      const adjustedBlueprint = {
        ...initialBlueprint,
        techStack: {
          ...initialBlueprint.techStack,
          frontend: [isBackendOnly ? "None" : frontendValue],
          backend: [isFrontendOnly ? "None" : backendValue],
          database: [databaseValue]
        }
      };

      setBlueprint(adjustedBlueprint);
    }
  }, [initialBlueprint, backendOptions, frontendOptions, databaseOptions]);

  const [editingOverview, setEditingOverview] = useState(false);
  const [editingFeature, setEditingFeature] = useState<string | null>(null);
  const [editingLayout, setEditingLayout] = useState<string | null>(null);
  const [newFeature, setNewFeature] = useState({ name: "", description: "" });
  const [newLayout, setNewLayout] = useState({ name: "", description: "" });
  const [addingFeature, setAddingFeature] = useState(false);
  const [addingLayout, setAddingLayout] = useState(false);
  const [editingLayoutDescription, setEditingLayoutDescription] = useState(false);
  const [deleteConfirmId, setDeleteConfirmId] = useState<string | null>(null);

  // Color picker refs to programmatically open color dialogs
  const primaryColorRef = useRef<HTMLInputElement>(null);
  const secondaryColorRef = useRef<HTMLInputElement>(null);
  const accentColorRef = useRef<HTMLInputElement>(null);
  const architectureDropdownRef = useRef<HTMLDivElement>(null);

  // Handle click outside for architecture dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (architectureDropdownRef.current && !architectureDropdownRef.current.contains(event.target as Node)) {
        setArchitectureDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Handle overview edit
  const handleOverviewChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setBlueprint((prev) => ({
      ...prev,
      description: e.target.value
    }));
  };

  // Handle feature toggle
  const handleFeatureToggle = (featureId: string) => {
    setBlueprint((prev) => ({
      ...prev,
      features: prev.features.map((feature) =>
        feature.id === featureId ? { ...feature, isEnabled: !feature.isEnabled } : feature
      )
    }));
  };

  // Handle feature edit
  const handleFeatureEdit = (featureId: string, field: string, value: string) => {
    setBlueprint((prev) => ({
      ...prev,
      features: prev.features.map((feature) =>
        feature.id === featureId ? { ...feature, [field]: value } : feature
      )
    }));
  };

  // Handle feature delete
  const handleDeleteFeature = (featureId: string) => {
    setBlueprint((prev) => ({
      ...prev,
      features: prev.features.filter((feature) => feature.id !== featureId)
    }));
    setDeleteConfirmId(null);
  };

  // Handle layout toggle
  const handleLayoutToggle = (layoutId: string) => {
    setBlueprint((prev) => ({
      ...prev,
      layouts: prev.layouts?.map((layout) =>
        layout.id === layoutId ? { ...layout, isEnabled: !layout.isEnabled } : layout
      ) || []
    }));
  };

  // Handle layout edit
  const handleLayoutEdit = (layoutId: string, field: string, value: string) => {
    setBlueprint((prev) => ({
      ...prev,
      layouts: prev.layouts?.map((layout) =>
        layout.id === layoutId ? { ...layout, [field]: value } : layout
      ) || []
    }));
  };

  // Handle tech stack change with batched updates to prevent flickering
  const handleTechStackChange = (category: 'frontend' | 'backend' | 'database', value: string) => {
    // Use the utility function to create the updated tech stack
    const newTechStack = createTechStackUpdate(blueprint.techStack, category, value, backendOptions);

    // Update the entire blueprint with the new tech stack in a single operation
    setBlueprint(prev => ({
      ...prev,
      techStack: newTechStack
    }));
  };

  // Handle color change
  const handleColorChange = (colorType: string, value: string) => {
    setBlueprint((prev) => ({
      ...prev,
      colors: {
        ...prev.colors,
        [colorType]: value
      }
    }));
  };

  // Handle theme change
  const handleThemeChange = (theme: 'light' | 'dark' | 'auto') => {
    setBlueprint((prev) => ({
      ...prev,
      theme
    }));
  };

  // Handle layout description change
  const handleLayoutDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setBlueprint((prev) => ({
      ...prev,
      layoutDescription: e.target.value
    }));
  };

  // Add new feature
  const handleAddFeature = () => {
    if (newFeature.name.trim() === "") return;

    setBlueprint((prev) => ({
      ...prev,
      features: [
        ...prev.features,
        {
          id: Math.random().toString(36).substring(2, 9),
          name: newFeature.name,
          description: newFeature.description,
          isEnabled: true
        }
      ]
    }));

    setNewFeature({ name: "", description: "" });
    setAddingFeature(false);
  };

  // Add new layout
  const handleAddLayout = () => {
    if (newLayout.name.trim() === "") return;

    setBlueprint((prev) => ({
      ...prev,
      layouts: [
        ...(prev.layouts || []),
        {
          id: Math.random().toString(36).substring(2, 9),
          name: newLayout.name,
          description: newLayout.description,
          isEnabled: true
        }
      ]
    }));

    setNewLayout({ name: "", description: "" });
    setAddingLayout(false);
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      {/* Project Overview */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <h3 className="typography-body-lg font-weight-medium text-gray-800">Project Overview</h3>
          <button
            onClick={() => setEditingOverview(!editingOverview)}
            className="text-[#f26a1b] hover:text-[#e05a0f] flex items-center"
          >
            <EditIcon />
            <span className="ml-1">Edit</span>
          </button>
        </div>

        {editingOverview ? (
          <div className="border border-gray-200 rounded-lg p-2">
            <textarea
              value={blueprint.description}
              onChange={handleOverviewChange}
              className="w-full p-2 rounded-md border-0 focus:ring-1 focus:ring-[#f26a1b]"
              rows={4}
            />
            <div className="flex justify-end mt-2">
              <button
                onClick={() => setEditingOverview(false)}
                className="px-3 py-1 bg-[#f26a1b] text-white rounded-md hover:bg-[#e05a0f] typography-body-sm flex items-center"
              >
                <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mr-1">
                  <path d="M20 6L9 17L4 12" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                Save
              </button>
            </div>
          </div>
        ) : (
          <div className="p-4 border border-gray-200 rounded-lg">
            <p className="text-gray-700">{blueprint.description}</p>
          </div>
        )}
      </div>

      {/* Core Features */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="typography-body-lg font-weight-medium text-gray-800">Core Features</h3>
          <button
            onClick={() => setAddingFeature(true)}
            className="flex items-center text-[#f26a1b] hover:text-[#e05a0f]"
          >
            <Plus size={16} className="mr-1" />
            Add Feature
          </button>
        </div>

        <div className="space-y-4">
          {/* Add Feature Form */}
          {addingFeature && (
            <div className="p-4 bg-neutral-100 rounded-lg mb-4">
              <div className="mb-3">
                <label className="block typography-body-sm font-weight-medium text-neutral-900 mb-1">
                  Feature Name
                </label>
                <input
                  type="text"
                  value={newFeature.name}
                  onChange={(e) => setNewFeature({...newFeature, name: e.target.value})}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-[#f26a1b] focus:border-[#f26a1b] outline-none"
                  placeholder="Feature name"
                />
              </div>
              <div className="mb-4">
                <label className="block typography-body-sm font-weight-medium text-neutral-900 mb-1">Description</label>
                <input
                  type="text"
                  value={newFeature.description}
                  onChange={(e) => setNewFeature({...newFeature, description: e.target.value})}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-[#f26a1b] focus:border-[#f26a1b] outline-none"
                  placeholder="Describe the feature"
                />
              </div>
              <div className="flex justify-end space-x-2">
                <button
                  onClick={() => setAddingFeature(false)}
                  className="px-3 py-1.5 border border-gray-300 text-gray-600 rounded-md hover:bg-gray-50 typography-body-sm"
                >
                  Cancel
                </button>
                <button
                  onClick={handleAddFeature}
                  className="px-3 py-1.5 bg-[#f26a1b] text-white rounded-md hover:bg-[#e05a0f] typography-body-sm"
                  disabled={!newFeature.name.trim()}
                >
                  Add Feature
                </button>
              </div>
            </div>
          )}

          {/* Feature List */}
          {blueprint.features.length === 0 ? (
            <div className="text-center py-8 bg-gray-50 rounded-lg border border-gray-200">
              <h4 className="typography-body-sm font-weight-medium text-gray-700 mb-2">No Features Added Yet</h4>
              <p className="typography-caption text-gray-500">
                Add features to define what your application will do and how users will interact with it.
              </p>
              {/* <button
                onClick={() => setAddingFeature(true)}
                className="px-4 py-2 bg-[#f26a1b] text-white rounded-md hover:bg-[#e05a0f] typography-body-sm flex items-center mx-auto"
              >
                <Plus size={16} className="mr-2" />
                Add Your First Feature
              </button> */}
            </div>
          ) : (
            blueprint.features.map((feature) => (
            <div key={feature.id} className="flex items-center justify-between bg-[#f9f9f9] rounded-lg p-4 mb-2">
              {editingFeature === feature.id ? (
                <div className="w-full">
                  <div className="mb-3">
                    <label className="block typography-body-sm font-weight-medium text-neutral-900 mb-1">
                      Feature Name
                    </label>
                    <input
                      type="text"
                      value={feature.name}
                      onChange={(e) => handleFeatureEdit(feature.id, 'name', e.target.value)}
                      className="w-full p-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-[#f26a1b] focus:border-[#f26a1b] outline-none"
                    />
                  </div>
                  <div className="mb-3">
                    <label className="block typography-body-sm font-weight-medium text-neutral-900 mb-1">Description</label>
                    <input
                      type="text"
                      value={feature.description}
                      onChange={(e) => handleFeatureEdit(feature.id, 'description', e.target.value)}
                      className="w-full p-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-[#f26a1b] focus:border-[#f26a1b] outline-none"
                    />
                  </div>
                  <div className="flex justify-end">
                    <button
                      onClick={() => setEditingFeature(null)}
                      className="px-3 py-1.5 bg-[#f26a1b] text-white rounded-md hover:bg-[#e05a0f] typography-body-sm flex items-center"
                    >
                      <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mr-1">
                        <path d="M20 6L9 17L4 12" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                      Save
                    </button>
                  </div>
                </div>
              ) : deleteConfirmId === feature.id ? (
                <div className="w-full">
                  <div className="flex items-center justify-between">
                    <p className="typography-body-sm text-gray-700">
                      Are you sure you want to delete this feature?
                    </p>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => setDeleteConfirmId(null)}
                        className="px-3 py-1 border border-gray-300 text-gray-600 rounded-md hover:bg-gray-50 typography-caption"
                      >
                        Cancel
                      </button>
                      <button
                        onClick={() => handleDeleteFeature(feature.id)}
                        className="px-3 py-1 bg-red-500 text-white rounded-md hover:bg-red-600 typography-caption flex items-center"
                      >
                        <Trash2 size={12} className="mr-1" />
                        Delete
                      </button>
                    </div>
                  </div>
                </div>
              ) : (
                <>
                  <div className="flex items-start">
                    {/* Generic feature icon */}
                    <div className="mr-4 text-[#f26a1b]">
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 11H15M9 15H12M12 3H7C5.89543 3 5 3.89543 5 5V19C5 20.1046 5.89543 21 7 21H17C18.1046 21 19 20.1046 19 19V9.82843C19 9.29799 18.7893 8.78929 18.4142 8.41421L13.5858 3.58579C13.2107 3.21071 12.702 3 12.1716 3H12Z" stroke="#f26a1b" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </div>

                    {/* Feature content */}
                    <div>
                      <h4 className="typography-body-sm font-weight-medium text-gray-900">{feature.name}</h4>
                      <p className="typography-caption text-gray-600">{feature.description}</p>
                    </div>
                  </div>

                  {/* Action buttons */}
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setEditingFeature(feature.id)}
                      className="text-[#f26a1b] hover:text-[#e05a0f]"
                      aria-label="Edit feature"
                    >
                      <EditIcon />
                    </button>
                    <button
                      onClick={() => setDeleteConfirmId(feature.id)}
                      className="text-gray-400 hover:text-red-500"
                      aria-label="Delete feature"
                    >
                      <Trash2 size={14} />
                    </button>
                  </div>
                </>
              )}
            </div>
          ))
          )}
        </div>
      </div>

      {/* Tech Stack */}
      <div className="mb-6">
        <TechStackSelection
          techStack={blueprint.techStack}
          frontendOptions={frontendOptions}
          backendOptions={backendOptions}
          databaseOptions={databaseOptions}
          onChange={handleTechStackChange}
          isDarkMode={isDarkMode}
        />
      </div>

      {/* Style Guidelines - Only show for frontend projects */}
      {!isBackendOnly && blueprint.colors && (
        <div className="mb-6">
          <h3 className="typography-body-lg font-weight-medium mb-4 text-gray-800">Style Guidelines</h3>

          {/* Color Palette */}
          <div className="mb-6">
            <div className="flex items-start">
              <div className="w-8 mr-4 text-gray-500 flex-shrink-0">
                <Palette size={24} className="text-[#f26a1b]" />
              </div>
              <div className="flex-1">
                <div className="flex space-x-8 items-center">
                  {/* Primary Color */}
                  <div className="text-center relative">
                    <div className="relative w-8 h-8 mb-2 mx-auto">
                      <div
                        className="w-8 h-8 rounded-full absolute top-0 left-0"
                        style={{ backgroundColor: blueprint.colors?.primary || "#F15A24" }}
                      />
                      <button
                        className="w-8 h-8 rounded-full absolute top-0 left-0 z-10 opacity-0 hover:opacity-100 flex items-center justify-center hover:bg-black hover:bg-opacity-50 transition-opacity duration-200 cursor-pointer border-0 p-0"
                        onClick={() => primaryColorRef.current?.click()}
                      >
                        <EditIcon color="white" />
                      </button>
                    </div>
                    <p className="typography-caption text-semantic-gray-500">Primary</p>
                    <input
                      ref={primaryColorRef}
                      type="color"
                      value={blueprint.colors?.primary || "#F15A24"}
                      onChange={(e) => handleColorChange('primary', e.target.value)}
                      className="absolute opacity-0 pointer-events-none"
                      aria-label="Select primary color"
                    />
                  </div>

                  {/* Secondary Color */}
                  <div className="text-center relative">
                    <div className="relative w-8 h-8 mb-2 mx-auto">
                      <div
                        className="w-8 h-8 rounded-full absolute top-0 left-0"
                        style={{ backgroundColor: blueprint.colors?.secondary || "#F97316" }}
                      />
                      <button
                        className="w-8 h-8 rounded-full absolute top-0 left-0 z-10 opacity-0 hover:opacity-100 flex items-center justify-center hover:bg-black hover:bg-opacity-50 transition-opacity duration-200 cursor-pointer border-0 p-0"
                        onClick={() => secondaryColorRef.current?.click()}
                      >
                        <EditIcon color="white" />
                      </button>
                    </div>
                    <p className="typography-caption text-gray-500">Secondary</p>
                    <input
                      ref={secondaryColorRef}
                      type="color"
                      value={blueprint.colors?.secondary || "#F97316"}
                      onChange={(e) => handleColorChange('secondary', e.target.value)}
                      className="absolute opacity-0 pointer-events-none"
                      aria-label="Select secondary color"
                    />
                  </div>

                  {/* Accent Color */}
                  <div className="text-center relative">
                    <div className="relative w-8 h-8 mb-2 mx-auto">
                      <div
                        className="w-8 h-8 rounded-full absolute top-0 left-0"
                        style={{ backgroundColor: blueprint.colors?.accent || "#64748B" }}
                      />
                      <button
                        className="w-8 h-8 rounded-full absolute top-0 left-0 z-10 opacity-0 hover:opacity-100 flex items-center justify-center hover:bg-black hover:bg-opacity-50 transition-opacity duration-200 cursor-pointer border-0 p-0"
                        onClick={() => accentColorRef.current?.click()}
                      >
                        <EditIcon color="white" />
                      </button>
                    </div>
                    <p className="typography-caption text-gray-500">Accent</p>
                    <input
                      ref={accentColorRef}
                      type="color"
                      value={blueprint.colors?.accent || "#64748B"}
                      onChange={(e) => handleColorChange('accent', e.target.value)}
                      className="absolute opacity-0 pointer-events-none"
                      aria-label="Select accent color"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Theme */}
          <div className="mb-6">
            <div className="flex items-start">
              <div className="w-8 mr-4 text-gray-500 flex-shrink-0">
                <Sun size={24} className="text-[#f26a1b]" />
              </div>
              <div className="flex-1">
                <div className="flex justify-between items-center mb-4">
                  <h4 className="font-weight-medium typography-body-sm text-gray-800">Theme</h4>
                </div>

                {/* Theme buttons */}
                <div className="mb-3 flex space-x-2">
                  <button
                    onClick={() => handleThemeChange('light')}
                    className={`px-4 py-2 rounded-md ${
                      blueprint.theme === 'light'
                        ? 'bg-primary-500 text-white'
                        : 'bg-white text-gray-700 border border-gray-300'
                    }`}
                  >
                    Light Mode
                  </button>
                  <button
                    onClick={() => handleThemeChange('dark')}
                    className={`px-4 py-2 rounded-md ${
                      blueprint.theme === 'dark'
                        ? 'bg-primary-500 text-white'
                        : 'bg-white text-gray-700 border border-gray-300'
                    }`}
                  >
                    Dark Mode
                  </button>
                  <button
                    onClick={() => handleThemeChange('auto')}
                    className={`px-4 py-2 rounded-md ${
                      blueprint.theme === 'auto'
                        ? 'bg-primary-500 text-white'
                        : 'bg-white text-gray-700 border border-gray-300'
                    }`}
                  >
                    Auto (System)
                  </button>
                </div>

                {/* Theme description */}
                <p className="typography-body-sm text-gray-600 max-w-2xl">
                  Support for {blueprint.theme === 'light' ? 'light' : blueprint.theme === 'dark' ? 'dark' : 'system-based'} mode with appropriate contrast ratios and readability.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Architecture/Layout Description */}
      <div className="mb-6">
        <div className="flex items-start">
          <div className="w-8 mr-4 text-gray-500 flex-shrink-0">
            <Layout size={24} className="text-[#f26a1b]" />
          </div>
          <div className="flex-1">
            <div className="flex justify-between items-center mb-2">
              <h4 className="font-weight-medium typography-body-sm text-gray-800">
                {isBackendOnly ? "API Architecture" : "Page Layout"}
              </h4>
              <button
                onClick={() => setEditingLayoutDescription(!editingLayoutDescription)}
                className="text-[#f26a1b] hover:text-[#e05a0f] flex items-center"
              >
                <EditIcon />
                <span className="ml-1">Edit</span>
              </button>
            </div>

            {editingLayoutDescription ? (
              <div className="border border-gray-200 rounded-lg p-2">
                <textarea
                  value={blueprint.layoutDescription}
                  onChange={handleLayoutDescriptionChange}
                  className="w-full p-2 rounded-md border-0 focus:ring-1 focus:ring-[#f26a1b]"
                  rows={4}
                  placeholder={isBackendOnly ? "Describe the API architecture and endpoint organization..." : "Describe the layout and design structure..."}
                />
                <div className="flex justify-end mt-2">
                  <button
                    onClick={() => setEditingLayoutDescription(false)}
                    className="px-3 py-1 bg-[#f26a1b] text-white rounded-md hover:bg-[#e05a0f] typography-body-sm flex items-center"
                  >
                    <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="mr-1">
                      <path d="M20 6L9 17L4 12" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    Save
                  </button>
                </div>
              </div>
            ) : (
              <div className="p-4 border border-gray-200 rounded-lg">
                <p className="text-gray-700">{blueprint.layoutDescription}</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* External Integrations */}
      <ExternalIntegrations />

      {/* Footer Info */}
      <div className="mt-8 p-4 bg-gray-50 rounded-lg">
        <p className="typography-body-sm text-gray-600 text-center">
          Kavia AI will generate your application based on these specifications
        </p>
      </div>
    </div>
  );
};

export default ProjectBlueprint;