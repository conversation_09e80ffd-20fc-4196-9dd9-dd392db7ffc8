import React, { useState } from 'react';
import { Template } from '../types';
import Image from 'next/image';
import { Eye } from 'lucide-react';

// Template data based on the images provided
const templates = [
  {
    id: '1',
    name: 'Dashboard',
    description: 'Analytics dashboard with data visualization and interactive controls.',
    thumbnail: '/templates/dashboard.jpg',
    imageUrl: '/templates/dashboard.jpg',
    category: 'web',
    framework: 'React',
    features: ['Data Visualization', 'Interactive Controls', 'Real-time Updates'],
    complexity: 'medium',
    tags: ['analytics', 'dashboard', 'visualization']
  },
  {
    id: '2',
    name: 'Landing Page',
    description: 'Beautiful, conversion-focused landing page with hero sections and calls-to-action.',
    thumbnail: '/templates/landing.jpg',
    imageUrl: '/templates/landing.jpg',
    category: 'web',
    framework: 'Next.js',
    features: ['Hero Section', 'Call-to-Action', 'Responsive Design'],
    complexity: 'simple',
    tags: ['marketing', 'landing', 'responsive']
  },
  {
    id: '3',
    name: 'Content Management',
    description: 'Flexible CMS with content editor, media library, and publishing workflow.',
    thumbnail: '/templates/cms.jpg',
    imageUrl: '/templates/cms.jpg',
    category: 'web',
    framework: 'Next.js',
    features: ['Content Editor', 'Media Library', 'Publishing Workflow'],
    complexity: 'complex',
    tags: ['cms', 'content', 'admin']
  },
  {
    id: '4',
    name: 'E-Commerce',
    description: 'Fully featured e-commerce platform with product catalog and checkout.',
    thumbnail: '/templates/ecommerce.jpg',
    imageUrl: '/templates/ecommerce.jpg',
    category: 'web',
    framework: 'React',
    features: ['Product Catalog', 'Shopping Cart', 'Checkout'],
    complexity: 'complex',
    tags: ['ecommerce', 'shop', 'retail']
  },
  {
    id: '5',
    name: 'E-Commerce Basic',
    description: 'Simple e-commerce store with essential features.',
    thumbnail: '/templates/ecommerce-basic.jpg',
    imageUrl: '/templates/ecommerce-basic.jpg',
    category: 'web',
    framework: 'React',
    features: ['Product Listing', 'Basic Cart', 'Checkout'],
    complexity: 'medium',
    tags: ['ecommerce', 'basic', 'shop']
  },
  {
    id: '6',
    name: 'E-Commerce Pro',
    description: 'Advanced e-commerce platform with inventory management.',
    thumbnail: '/templates/ecommerce-pro.jpg',
    imageUrl: '/templates/ecommerce-pro.jpg',
    category: 'web',
    framework: 'React',
    features: ['Product Management', 'Advanced Cart', 'Inventory', 'Analytics'],
    complexity: 'complex',
    tags: ['ecommerce', 'advanced', 'inventory']
  }
];

interface TemplateSelectorProps {
  onTemplateSelect: (template: Template) => void;
  selectedTemplate: Template | null;
}

const TemplateSelector: React.FC<TemplateSelectorProps> = ({ onTemplateSelect, selectedTemplate }) => {
  const [previewTemplate, setPreviewTemplate] = useState<string | null>(null);

  const handlePreviewClick = (e: React.MouseEvent, templateId: string) => {
    e.stopPropagation();
    setPreviewTemplate(templateId === previewTemplate ? null : templateId);
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {templates.map((template) => (
                      <div
            key={template.id}
            className={`border rounded-lg overflow-hidden cursor-pointer transition-all duration-200 ${
              selectedTemplate?.id === template.id
                ? 'ring-2 ring-orange-500 shadow-md'
                : 'hover:shadow-md hover:border-semantic-gray-300'
            }`}
            onClick={() => onTemplateSelect(template)}
          >
            <div className="relative h-40 w-full bg-semantic-gray-100">
              {/* Template Thumbnail */}
              <div className="relative w-full h-full">
                <Image
                  src={template.thumbnail}
                  alt={template.name}
                  layout="fill"
                  objectFit="cover"
                />
                {/* Preview Icon */}
                <div 
                  className="absolute top-2 right-2 p-1.5 bg-white bg-opacity-80 rounded-full cursor-pointer hover:bg-opacity-100 transition-all"
                  onClick={(e) => handlePreviewClick(e, template.id)}
                >
                  <Eye size={18} className="text-semantic-gray-700" />
                </div>
              </div>
            </div>
            <div className="p-4">
              <h3 className="typography-body-lg font-weight-medium text-semantic-gray-800">{template.name}</h3>
              <p className="typography-body-sm text-semantic-gray-600 mt-1 min-h-[3rem]">{template.description}</p>
            </div>
          </div>
        ))}
      </div>

      {/* Preview Modal */}
      {previewTemplate && (
        <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50" onClick={() => setPreviewTemplate(null)}>
          <div className="bg-white rounded-lg max-w-4xl max-h-[90vh] overflow-y-auto p-4" onClick={(e) => e.stopPropagation()}>
            <div className="flex justify-between items-center mb-4">
              <h3 className="typography-heading-4 font-weight-medium">
                {templates.find(t => t.id === previewTemplate)?.name} Preview
              </h3>
              <button onClick={() => setPreviewTemplate(null)} className="text-semantic-gray-500 hover:text-semantic-gray-700">✕</button>
            </div>
            <div className="w-full h-[60vh] bg-semantic-gray-100 rounded-md relative">
              <Image
                src={templates.find(t => t.id === previewTemplate)?.thumbnail || ''}
                alt="Template Preview"
                layout="fill"
                objectFit="contain"
              />
            </div>
            <div className="mt-4">
              <p className="text-semantic-gray-700">{templates.find(t => t.id === previewTemplate)?.description}</p>
              <div className="mt-2 flex flex-wrap gap-2">
                {templates.find(t => t.id === previewTemplate)?.features.map((feature, idx) => (
                  <span key={idx} className="px-2 py-1 bg-semantic-gray-100 typography-body-sm text-semantic-gray-700 rounded-full">
                    {feature}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TemplateSelector;