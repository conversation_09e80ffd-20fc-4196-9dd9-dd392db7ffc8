// File: components/UIComponents/CreditBadge/CreditBadge.jsx
import React, { useState } from 'react';
import { RefreshCw, Zap } from 'lucide-react';

const CreditBadge = ({ 
  planCredits, 
  organizationCost, 
  isRefreshing, 
  onRefresh,
  className = "" // Allow custom styling
}) => {
  const [isHovered, setIsHovered] = useState(false);
  
  // Loading state
  if (planCredits === null || organizationCost === null) {
    return (
      <div className={`flex items-center gap-2 px-3 py-1.5 rounded-full border border-semantic-gray-200 bg-semantic-gray-50 ${className}`}>
        <div className="h-4 w-4 bg-semantic-gray-300 rounded animate-pulse" />
        <span className="text-sm text-semantic-gray-500">Loading credits...</span>
      </div>
    );
  }
  
  const remainingCredits = Math.max(0, Math.round(planCredits - organizationCost * 20000));
  const creditPercentage = (remainingCredits / planCredits) * 100;
  
  // Determine badge style based on remaining credits percentage
  const getBadgeStyle = () => {
    if (creditPercentage > 50) {
      return 'bg-green-50 border-green-200 text-green-800 hover:bg-green-100';
    } else if (creditPercentage > 20) {
      return 'bg-yellow-50 border-yellow-200 text-yellow-800 hover:bg-yellow-100';
    } else {
      return 'bg-red-50 border-red-200 text-red-800 hover:bg-red-100';
    }
  };

  // Determine icon color based on remaining credits percentage
  const getIconColor = () => {
    if (creditPercentage > 50) return 'text-green-600';
    if (creditPercentage > 20) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div 
      className={`flex items-center gap-2 px-3 py-1.5 rounded-full border transition-all duration-200 cursor-pointer ${getBadgeStyle()} ${className}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={onRefresh}
      title="Click to refresh credits"
      role="button"
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          onRefresh();
        }
      }}
    >
      <Zap className={`h-4 w-4 ${getIconColor()}`} />
      <span className="text-sm font-medium">
        {isRefreshing ? 'Refreshing...' : `${remainingCredits.toLocaleString()} Credits`}
      </span>
      <RefreshCw 
        className={`h-3.5 w-3.5 transition-transform duration-200 ${
          isRefreshing ? 'animate-spin' : isHovered ? 'rotate-90' : ''
        } ${getIconColor()}`} 
      />
    </div>
  );
};

export default CreditBadge;