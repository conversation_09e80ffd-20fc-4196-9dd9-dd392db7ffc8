/* component page alignment start */
.heading{
    @apply text-[16px] font-bold text-[hsl(var(--semantic-gray-700))] whitespace-normal max-w-[400px] break-words
}
.badge {
    @apply py-1
}
.buttons {
    @apply flex items-center justify-end flex-shrink-0 space-x-1.5
}
.notFound {
    @apply flex items-center justify-center -mt-[5px]
}
/* component tab css alignment ends */
/* component detail page css alignment starts */
/* .componentWrapper { */
/* .componentSubWrapper { */
/* .outerBorder { */