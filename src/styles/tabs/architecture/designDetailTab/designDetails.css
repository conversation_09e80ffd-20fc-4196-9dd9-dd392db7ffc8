@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  /* Design details tab alignment starts */
  .design-details-content-wrapper {
      @apply overflow-y-auto max-h-[74vh] text-black tracking-[0.2px];
      font-size: var(--font-size-base);
      font-weight: var(--font-weight-normal);
      line-height: var(--line-height-normal);
  }
  .design-details-content-sub-wrapper {
      @apply mb-6 bg-white p-0;
  }
  .design-details-container {
      /* @apply p-4 space-y-4 */
      @apply space-y-4;
  }
  .design-details-header-wrapper {
      @apply flex sticky top-0 p-2 justify-between items-center border rounded-lg h-[80px] z-20;
  }
  .design-details-header-sub-wrapper {
      @apply flex items-center flex-grow max-h-full flex-wrap ml-2;
  }
  .design-details-heading-title {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      line-height: var(--line-height-tight);
      @apply text-[hsl(var(--semantic-gray-700))] whitespace-normal max-w-[400px] break-words;
  }
  .design-details-heading-badge {
      @apply py-1 ml-2;
  }
  .design-details-header-button-wrapper {
      @apply flex items-center justify-end flex-shrink-0;
  }
  .design-details-related-child-nodes-wrapper {
      /* @apply mt-8 mx-5  */
  }
  .design-details-no-items-found {
      @apply flex items-center justify-center -mt-[5px];
  }
  /* Design details tab alignment end  */
}