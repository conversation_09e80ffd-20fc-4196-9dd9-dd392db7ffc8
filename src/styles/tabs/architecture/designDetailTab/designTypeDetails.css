@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  /* design type details page alignment start*/

  .design-type-details-content-wrapper {
      @apply overflow-y-auto max-h-[74vh] text-black tracking-[0.2px];
      font-size: var(--font-size-base);
      font-weight: var(--font-weight-normal);
      line-height: var(--line-height-normal);
  }

  .design-type-details-content-sub-wrapper {
      @apply mb-6 bg-white;
  }

  .design-type-details-container {
      /* @apply p-4 space-y-4 */
      @apply space-y-4;
  }

  .design-type-details-header-wrapper {
      @apply flex sticky top-0 p-2 justify-between items-center border rounded-lg h-[80px] z-20;
  }

  .design-type-details-header-sub-wrapper {
      @apply flex items-center flex-grow max-h-full flex-wrap ml-2;
  }

  .design-type-details-header-back-button {
      @apply px-2 py-2 text-semantic-gray-600 hover:text-semantic-gray-800 transition-colors duration-200 flex items-center;
  }
  .design-type-details-header-back-icon-wrapper {
      @apply bg-semantic-gray-200 rounded-full p-2 inline-flex items-center justify-center;
  }
  .design-type-details-header-back-icon {
      @apply h-5 w-5 text-semantic-gray-700;
  }

  .design-type-details-heading-title  {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      line-height: var(--line-height-tight);
      @apply text-[hsl(var(--semantic-gray-700))] whitespace-normal max-w-[400px] break-words;
  }

  .design-type-details-heading-badge {
      @apply py-1 ml-2;
  }

  .design-type-details-header-button-wrapper {
      @apply flex items-center justify-end flex-shrink-0;
  }

  .design-type-details-no-items-found {
      @apply flex justify-center items-center text-center;
  }

  /* design type details page alignment end */
}