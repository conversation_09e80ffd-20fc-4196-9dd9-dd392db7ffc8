/* overview page alignment  start*/
.overviewTabWrapper {
   @apply flex flex-col min-h-[60vh] max-h-[calc(100vh-60px)] w-full pt-4
}
.overviewContainerPadding {
    @apply top-0
}
.project-panel-title {
    font-size: var(--font-size-xl);
    line-height: var(--line-height-tight);
    font-weight: var(--font-weight-semibold);
}
.userIconDiv {
   @apply flex items-center
}
.taskCount {
    @apply ml-2 text-lg font-bold text-semantic-gray-500
}
.noProjectFound {
    @apply flex justify-center items-center  text-center
}
/* overview page alignment end */