#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');

const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);
const readFile = promisify(fs.readFile);

// Extensions to check
const extensions = ['.js', '.jsx', '.ts', '.tsx', '.css', '.scss'];

// Patterns to look for (these should be mostly gone now)
// Note: We exclude semantic-gray patterns as those are the correct replacements
const oldPatterns = [
  {
    pattern: /\b(?:text|bg|border)-gray-\d+\b/g,
    exclude: /semantic-gray/,
    description: 'Tailwind gray classes (excluding semantic-gray)'
  },
  {
    pattern: /\bhover:(?:text|bg|border)-gray-\d+\b/g,
    exclude: /semantic-gray/,
    description: 'Hover gray classes (excluding semantic-gray)'
  },
  {
    pattern: /\bfocus:(?:ring|border)-gray-\d+\b/g,
    exclude: /semantic-gray/,
    description: 'Focus gray classes (excluding semantic-gray)'
  },
  {
    pattern: /#[Ff][39][Ff][Aa][Ff][Bb]/g,
    description: 'Hex color #F9FAFB'
  },
  {
    pattern: /#[Ff][34][Ff][46][Ff][46]/g,
    description: 'Hex color #F3F4F6'
  },
  {
    pattern: /#[Ee][56][Ee][78][Ee][Bb]/g,
    description: 'Hex color #E5E7EB'
  },
  {
    pattern: /#[Dd][12][Dd][56][Dd][Bb]/g,
    description: 'Hex color #D1D5DB'
  },
  {
    pattern: /#[9][Cc][Aa][34][Aa][Ff]/g,
    description: 'Hex color #9CA3AF'
  },
  {
    pattern: /#[67][Bb][78][23][89][0]/g,
    description: 'Hex color #6B7280'
  },
  {
    pattern: /#[45][Bb][56][56][67][34]/g,
    description: 'Hex color #4B5563'
  },
  {
    pattern: /#[34][78][45][12][56][12]/g,
    description: 'Hex color #374151'
  },
  {
    pattern: /#[12][Ff][23][9][34][78]/g,
    description: 'Hex color #1F2937'
  },
  {
    pattern: /#[12][12][12][89][23][78]/g,
    description: 'Hex color #111827'
  },
  {
    pattern: /#[23][Aa][34][45][34][9]/g,
    description: 'Hex color #2A3439'
  },
  {
    pattern: /#[Ee][0][Ee][0][Ee][0]/g,
    description: 'Hex color #e0e0e0'
  },
  {
    pattern: /#[23][34][12][Ff][23][0]/g,
    description: 'Hex color #231f20'
  },
  {
    pattern: /\btext-gray-1000\b/g,
    description: 'Invalid Tailwind class text-gray-1000'
  },
  {
    pattern: /\bborder-gray-250\b/g,
    description: 'Invalid Tailwind class border-gray-250'
  }
];

async function getFiles(dir) {
  const subdirs = await readdir(dir);
  const files = await Promise.all(
    subdirs.map(async (subdir) => {
      const res = path.resolve(dir, subdir);
      return (await stat(res)).isDirectory() ? getFiles(res) : res;
    })
  );
  return files
    .flat()
    .filter((file) => 
      extensions.includes(path.extname(file)) && 
      !file.includes('node_modules') && 
      !file.includes('.next') &&
      !file.includes('backup-') &&
      !file.includes('replace-hardcoded-gray-colors.js') &&
      !file.includes('verify-gray-replacement.js')
    );
}

function checkForOldPatterns(content, filePath) {
  const issues = [];

  for (const patternObj of oldPatterns) {
    const { pattern, exclude, description } = patternObj;
    const matches = content.match(pattern);

    if (matches) {
      // Filter out matches that should be excluded (like semantic-gray)
      const filteredMatches = exclude
        ? matches.filter(match => !exclude.test(match))
        : matches;

      if (filteredMatches.length > 0) {
        issues.push({
          pattern: description || pattern.source,
          matches: filteredMatches,
          count: filteredMatches.length
        });
      }
    }
  }

  return issues;
}

function checkForNewPatterns(content) {
  const semanticGrayMatches = content.match(/semantic-gray-\d+/g) || [];
  const hslVarMatches = content.match(/hsl\(var\(--semantic-gray-\d+\)\)/g) || [];
  
  return {
    semanticGrayCount: semanticGrayMatches.length,
    hslVarCount: hslVarMatches.length,
    totalThemeUsage: semanticGrayMatches.length + hslVarMatches.length
  };
}

async function main() {
  console.log('🔍 Gray Color Replacement Verification');
  console.log('=====================================');
  console.log('');

  try {
    const files = await getFiles('./src');
    console.log(`📁 Checking ${files.length} files for remaining hardcoded gray colors...\n`);

    let totalIssues = 0;
    let totalFiles = 0;
    let totalThemeUsage = 0;
    let filesWithIssues = [];

    for (const file of files) {
      try {
        const content = await readFile(file, 'utf8');
        const issues = checkForOldPatterns(content, file);
        const themeUsage = checkForNewPatterns(content);
        
        totalThemeUsage += themeUsage.totalThemeUsage;

        if (issues.length > 0) {
          console.log(`⚠️  ${file}:`);
          issues.forEach(issue => {
            console.log(`   - ${issue.pattern}: ${issue.count} matches`);
            issue.matches.forEach(match => {
              console.log(`     "${match}"`);
            });
          });
          console.log('');
          
          totalIssues += issues.reduce((sum, issue) => sum + issue.count, 0);
          totalFiles++;
          filesWithIssues.push(file);
        }
      } catch (error) {
        console.warn(`⚠️  Warning: Could not process ${file}: ${error.message}`);
      }
    }

    console.log('📊 Verification Summary:');
    console.log(`   Files with remaining hardcoded grays: ${totalFiles}`);
    console.log(`   Total remaining hardcoded patterns: ${totalIssues}`);
    console.log(`   Total theme-based gray usage: ${totalThemeUsage}`);
    console.log('');

    if (totalIssues === 0) {
      console.log('✅ SUCCESS: No hardcoded gray colors found!');
      console.log('🎉 All gray colors have been successfully replaced with theme variables.');
      console.log(`📈 Found ${totalThemeUsage} instances of proper theme usage.`);
    } else {
      console.log('❌ ISSUES FOUND: Some hardcoded gray colors remain.');
      console.log('');
      console.log('Files that need attention:');
      filesWithIssues.forEach(file => {
        console.log(`   - ${file}`);
      });
      console.log('');
      console.log('💡 You may want to run the replacement script again or manually fix these files.');
    }

    console.log('');
    console.log('📝 Next steps:');
    console.log('   1. Test your application in both light and dark themes');
    console.log('   2. Run your build process to ensure no compilation errors');
    console.log('   3. Check the browser console for any missing CSS classes');
    console.log('   4. Verify that all gray colors display correctly');

  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { checkForOldPatterns, checkForNewPatterns };
